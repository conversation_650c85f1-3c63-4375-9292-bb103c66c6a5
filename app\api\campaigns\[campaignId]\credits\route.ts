import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getCampaignCredit, getCampaignCreditTransactions } from '@/lib/models/campaignCredit';

export async function GET(
  request: Request,
  { params }: { params: { campaignId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { campaignId } = params;
    const userId = session.user.id;

    // Get campaign credit information
    const campaignCredit = await getCampaignCredit(userId, campaignId);

    if (!campaignCredit) {
      return NextResponse.json({ error: 'Campaign credit record not found' }, { status: 404 });
    }

    // Get transactions
    const transactions = await getCampaignCreditTransactions(userId, campaignId);

    return NextResponse.json({
      campaignId,
      campaignName: campaignCredit.campaignName,
      totalCredits: campaignCredit.totalCredits,
      remainingCredits: campaignCredit.remainingCredits,
      usedCredits: campaignCredit.usedCredits,
      transactions: transactions.map(t => ({
        ...t,
        date: t.date.toISOString(),
      })),
      createdAt: campaignCredit.createdAt.toISOString(),
      updatedAt: campaignCredit.updatedAt.toISOString(),
    });
  } catch (error) {
    console.error('API Error fetching campaign credits:', error);
    return NextResponse.json({ error: 'Failed to fetch campaign credits' }, { status: 500 });
  }
}
