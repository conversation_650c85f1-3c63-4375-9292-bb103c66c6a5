/**
 * Script: Add Free Credits to Existing Campaigns
 * 
 * This script adds 50 free credits to all existing campaigns that don't have enough credits.
 */

import clientPromise from '../lib/mongodb';
import { ObjectId } from 'mongodb';

interface CampaignCredit {
  _id: ObjectId;
  userId: ObjectId;
  campaignId: ObjectId;
  campaignName: string;
  totalCredits: number;
  remainingCredits: number;
  usedCredits: number;
  transactions: Array<{
    id: string;
    date: Date;
    amount: number;
    type: string;
    description: string;
  }>;
}

interface Game {
  _id: ObjectId;
  userId: ObjectId;
  name: string;
  prizes: Array<{
    quantity: number;
    remainingQuantity: number;
  }>;
}

const FREE_CAMPAIGN_CREDITS = 50;

async function addFreeCreditsToExistingCampaigns() {
  console.log('🎁 Adding free credits to existing campaigns...');

  try {
    const client = await clientPromise;
    const db = client.db();

    // Get all campaign credits
    console.log('📊 Fetching existing campaign credits...');
    const campaignCredits = await db.collection('campaignCredits').find({}).toArray() as CampaignCredit[];
    console.log(`Found ${campaignCredits.length} campaign credit records`);

    // Get all games
    console.log('🎮 Fetching all games...');
    const games = await db.collection('games').find({}).toArray() as Game[];
    console.log(`Found ${games.length} games`);

    let updatedCampaigns = 0;
    let addedCredits = 0;

    // Process each campaign credit record
    for (const campaignCredit of campaignCredits) {
      console.log(`\n🎯 Processing campaign: ${campaignCredit.campaignName}`);
      console.log(`   Current credits: ${campaignCredit.remainingCredits}/${campaignCredit.totalCredits}`);

      // Check if campaign already has enough free credits
      if (campaignCredit.totalCredits >= FREE_CAMPAIGN_CREDITS) {
        console.log(`   ✅ Campaign already has ${campaignCredit.totalCredits} credits (>= ${FREE_CAMPAIGN_CREDITS}). Skipping...`);
        continue;
      }

      // Calculate how many free credits to add
      const creditsToAdd = FREE_CAMPAIGN_CREDITS - campaignCredit.totalCredits;
      console.log(`   💰 Adding ${creditsToAdd} free credits`);

      // Create transaction record
      const freeCreditsTransaction = {
        id: new ObjectId().toString(),
        date: new Date(),
        amount: creditsToAdd,
        type: 'purchase',
        description: `جوایز رایگان اضافی - ${creditsToAdd} جایزه (به‌روزرسانی سیستم)`,
      };

      // Update campaign credit record
      const updateResult = await db.collection('campaignCredits').updateOne(
        { _id: campaignCredit._id },
        {
          $inc: {
            totalCredits: creditsToAdd,
            remainingCredits: creditsToAdd,
          },
          $push: { transactions: freeCreditsTransaction },
          $set: { updatedAt: new Date() },
        }
      );

      if (updateResult.modifiedCount > 0) {
        updatedCampaigns++;
        addedCredits += creditsToAdd;
        console.log(`   ✅ Successfully added ${creditsToAdd} free credits`);
      } else {
        console.log(`   ❌ Failed to update campaign credits`);
      }
    }

    // Check for games without campaign credit records
    console.log('\n🔍 Checking for games without campaign credit records...');
    let createdRecords = 0;

    for (const game of games) {
      const existingCredit = campaignCredits.find(
        credit => credit.campaignId.toString() === game._id.toString()
      );

      if (!existingCredit) {
        console.log(`\n🆕 Creating campaign credit record for: ${game.name}`);
        
        // Calculate total prizes for this game
        const totalPrizes = game.prizes?.reduce((sum, prize) => sum + prize.quantity, 0) || 0;
        const usedPrizes = game.prizes?.reduce((sum, prize) => sum + (prize.quantity - prize.remainingQuantity), 0) || 0;
        
        // Give at least 50 free credits
        const initialCredits = Math.max(totalPrizes, FREE_CAMPAIGN_CREDITS);
        const remainingCredits = Math.max(0, initialCredits - usedPrizes);

        const freeCreditsTransaction = {
          id: new ObjectId().toString(),
          date: new Date(),
          amount: initialCredits,
          type: 'purchase',
          description: `جوایز رایگان کمپین - ${initialCredits} جایزه (به‌روزرسانی سیستم)`,
        };

        const newCampaignCredit = {
          userId: game.userId,
          campaignId: game._id,
          campaignName: game.name,
          totalCredits: initialCredits,
          remainingCredits: remainingCredits,
          usedCredits: usedPrizes,
          transactions: [freeCreditsTransaction],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await db.collection('campaignCredits').insertOne(newCampaignCredit);
        createdRecords++;
        addedCredits += initialCredits;
        console.log(`   ✅ Created with ${initialCredits} credits (${remainingCredits} remaining)`);
      }
    }

    console.log('\n🎉 Free credits distribution completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Campaigns updated: ${updatedCampaigns}`);
    console.log(`   - New records created: ${createdRecords}`);
    console.log(`   - Total credits added: ${addedCredits}`);
    console.log(`   - Free credits per campaign: ${FREE_CAMPAIGN_CREDITS}`);

  } catch (error) {
    console.error('❌ Failed to add free credits:', error);
    throw error;
  }
}

// Verification function
async function verifyFreeCredits() {
  console.log('\n🔍 Verifying free credits distribution...');

  try {
    const client = await clientPromise;
    const db = client.db();

    const campaignCredits = await db.collection('campaignCredits').find({}).toArray();
    const games = await db.collection('games').countDocuments();

    console.log('📊 Verification Results:');
    console.log(`   - Total campaign credit records: ${campaignCredits.length}`);
    console.log(`   - Total games: ${games}`);

    let campaignsWithEnoughCredits = 0;
    let totalCreditsDistributed = 0;

    for (const credit of campaignCredits) {
      totalCreditsDistributed += credit.totalCredits;
      if (credit.totalCredits >= FREE_CAMPAIGN_CREDITS) {
        campaignsWithEnoughCredits++;
      } else {
        console.log(`   ⚠️  Campaign "${credit.campaignName}" has only ${credit.totalCredits} credits`);
      }
    }

    console.log(`   - Campaigns with >= ${FREE_CAMPAIGN_CREDITS} credits: ${campaignsWithEnoughCredits}/${campaignCredits.length}`);
    console.log(`   - Total credits distributed: ${totalCreditsDistributed}`);
    console.log(`   - Average credits per campaign: ${Math.round(totalCreditsDistributed / campaignCredits.length)}`);

    if (campaignsWithEnoughCredits === campaignCredits.length) {
      console.log('   ✅ All campaigns have sufficient free credits!');
    } else {
      console.log(`   ⚠️  ${campaignCredits.length - campaignsWithEnoughCredits} campaigns need more credits`);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'add':
      await addFreeCreditsToExistingCampaigns();
      break;
    case 'verify':
      await verifyFreeCredits();
      break;
    case 'both':
      await addFreeCreditsToExistingCampaigns();
      await verifyFreeCredits();
      break;
    default:
      console.log('Usage: npm run free-credits [add|verify|both]');
      console.log('  add    - Add free credits to existing campaigns');
      console.log('  verify - Verify free credits distribution');
      console.log('  both   - Add free credits and verify');
      break;
  }

  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

export { addFreeCreditsToExistingCampaigns, verifyFreeCredits };
