/**
 * Migration Script: Convert User Credits to Campaign-Specific Credits
 * 
 * This script migrates the existing user credit system to the new campaign-specific credit system.
 * It creates CampaignCredit records for each existing game/campaign.
 */

import clientPromise from '../lib/mongodb';
import { ObjectId } from 'mongodb';

interface User {
  _id: ObjectId;
  credits: number;
  name: string;
  email?: string;
  mobileNumber?: string;
}

interface Game {
  _id: ObjectId;
  userId: ObjectId;
  name: string;
  prizes: Array<{
    type: string;
    description: string;
    probability: number;
    quantity: number;
    remainingQuantity: number;
  }>;
  createdAt: Date;
}

interface CampaignCredit {
  userId: ObjectId;
  campaignId: ObjectId;
  campaignName: string;
  totalCredits: number;
  remainingCredits: number;
  usedCredits: number;
  transactions: Array<{
    id: string;
    date: Date;
    amount: number;
    type: 'migration' | 'purchase' | 'usage';
    description: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

async function migrateToCampaignCredits() {
  console.log('🚀 Starting migration to campaign-specific credits...');

  try {
    const client = await clientPromise;
    const db = client.db();

    // Get all users with credits
    console.log('📊 Fetching users with credits...');
    const users = await db.collection('users').find({ credits: { $gt: 0 } }).toArray() as User[];
    console.log(`Found ${users.length} users with credits`);

    // Get all games
    console.log('🎮 Fetching all games...');
    const games = await db.collection('games').find({}).toArray() as Game[];
    console.log(`Found ${games.length} games`);

    // Check if campaign credits collection already exists
    const existingCampaignCredits = await db.collection('campaignCredits').countDocuments();
    if (existingCampaignCredits > 0) {
      console.log(`⚠️  Warning: Found ${existingCampaignCredits} existing campaign credit records`);
      console.log('Migration may have already been run. Proceeding with caution...');
    }

    let migratedUsers = 0;
    let createdCampaignCredits = 0;

    // Process each user
    for (const user of users) {
      console.log(`\n👤 Processing user: ${user.name} (${user.email || user.mobileNumber})`);
      console.log(`   Current credits: ${user.credits}`);

      // Find games belonging to this user
      const userGames = games.filter(game => game.userId.toString() === user._id.toString());
      console.log(`   Found ${userGames.length} games for this user`);

      if (userGames.length === 0) {
        console.log(`   ⚠️  User has ${user.credits} credits but no games. Skipping...`);
        continue;
      }

      // Calculate total prizes needed across all user's games
      let totalPrizesNeeded = 0;
      for (const game of userGames) {
        const gamePrizes = game.prizes?.reduce((sum, prize) => sum + prize.quantity, 0) || 0;
        totalPrizesNeeded += gamePrizes;
      }

      console.log(`   Total prizes needed across all games: ${totalPrizesNeeded}`);

      // Distribute user's credits among their games
      let remainingCredits = user.credits;

      for (let i = 0; i < userGames.length; i++) {
        const game = userGames[i];
        const gamePrizes = game.prizes?.reduce((sum, prize) => sum + prize.quantity, 0) || 0;
        const gameUsedPrizes = game.prizes?.reduce((sum, prize) => sum + (prize.quantity - prize.remainingQuantity), 0) || 0;

        // Calculate credits to allocate to this game
        let creditsForThisGame: number;
        
        if (i === userGames.length - 1) {
          // Last game gets all remaining credits
          creditsForThisGame = remainingCredits;
        } else {
          // Distribute proportionally based on game's prize needs
          const proportion = totalPrizesNeeded > 0 ? gamePrizes / totalPrizesNeeded : 1 / userGames.length;
          creditsForThisGame = Math.floor(user.credits * proportion);
        }

        // Ensure we don't allocate more credits than needed for this game
        creditsForThisGame = Math.max(0, Math.min(creditsForThisGame, gamePrizes));

        console.log(`   🎯 Game: ${game.name}`);
        console.log(`      Prizes: ${gamePrizes}, Used: ${gameUsedPrizes}, Remaining: ${gamePrizes - gameUsedPrizes}`);
        console.log(`      Allocating ${creditsForThisGame} credits`);

        // Check if campaign credit already exists
        const existingCampaignCredit = await db.collection('campaignCredits').findOne({
          userId: user._id,
          campaignId: game._id,
        });

        if (existingCampaignCredit) {
          console.log(`      ⚠️  Campaign credit already exists. Skipping...`);
          continue;
        }

        // Create campaign credit record
        const campaignCredit: CampaignCredit = {
          userId: user._id,
          campaignId: game._id,
          campaignName: game.name,
          totalCredits: creditsForThisGame,
          remainingCredits: Math.max(0, creditsForThisGame - gameUsedPrizes),
          usedCredits: Math.min(gameUsedPrizes, creditsForThisGame),
          transactions: [
            {
              id: new ObjectId().toString(),
              date: new Date(),
              amount: creditsForThisGame,
              type: 'migration',
              description: `مایگریشن از سیستم اعتبار کاربر - ${user.credits} اعتبار کل`,
            },
          ],
          createdAt: game.createdAt || new Date(),
          updatedAt: new Date(),
        };

        await db.collection('campaignCredits').insertOne(campaignCredit);
        createdCampaignCredits++;

        remainingCredits -= creditsForThisGame;
      }

      migratedUsers++;
      console.log(`   ✅ Completed migration for user ${user.name}`);
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Users processed: ${migratedUsers}`);
    console.log(`   - Campaign credit records created: ${createdCampaignCredits}`);
    console.log(`   - Total users with credits: ${users.length}`);
    console.log(`   - Total games: ${games.length}`);

    console.log('\n⚠️  Important Notes:');
    console.log('   - User credits are still preserved in the users collection');
    console.log('   - You may want to set user credits to 0 after verifying the migration');
    console.log('   - Run verification queries to ensure data integrity');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Verification function to check migration results
async function verifyMigration() {
  console.log('\n🔍 Running migration verification...');

  try {
    const client = await clientPromise;
    const db = client.db();

    const usersWithCredits = await db.collection('users').countDocuments({ credits: { $gt: 0 } });
    const totalCampaignCredits = await db.collection('campaignCredits').countDocuments();
    const totalGames = await db.collection('games').countDocuments();

    console.log('📊 Verification Results:');
    console.log(`   - Users with credits: ${usersWithCredits}`);
    console.log(`   - Campaign credit records: ${totalCampaignCredits}`);
    console.log(`   - Total games: ${totalGames}`);

    // Check for orphaned campaign credits
    const campaignCredits = await db.collection('campaignCredits').find({}).toArray();
    let orphanedCredits = 0;

    for (const credit of campaignCredits) {
      const gameExists = await db.collection('games').findOne({ _id: credit.campaignId });
      const userExists = await db.collection('users').findOne({ _id: credit.userId });

      if (!gameExists || !userExists) {
        orphanedCredits++;
        console.log(`   ⚠️  Orphaned credit found: Campaign ${credit.campaignId}, User ${credit.userId}`);
      }
    }

    if (orphanedCredits === 0) {
      console.log('   ✅ No orphaned campaign credits found');
    } else {
      console.log(`   ⚠️  Found ${orphanedCredits} orphaned campaign credits`);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'migrate':
      await migrateToCampaignCredits();
      break;
    case 'verify':
      await verifyMigration();
      break;
    case 'both':
      await migrateToCampaignCredits();
      await verifyMigration();
      break;
    default:
      console.log('Usage: npm run migrate [migrate|verify|both]');
      console.log('  migrate - Run the migration');
      console.log('  verify  - Verify migration results');
      console.log('  both    - Run migration and verification');
      break;
  }

  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

export { migrateToCampaignCredits, verifyMigration };
