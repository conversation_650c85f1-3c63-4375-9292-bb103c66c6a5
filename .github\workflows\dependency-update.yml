name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Allow manual trigger

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'

    - name: Update dependencies
      run: |
        npm update
        npm audit fix --audit-level moderate

    - name: Check for changes
      id: changes
      run: |
        if git diff --quiet package-lock.json; then
          echo "has_changes=false" >> $GITHUB_OUTPUT
        else
          echo "has_changes=true" >> $GITHUB_OUTPUT
        fi

    - name: Create Pull Request
      if: steps.changes.outputs.has_changes == 'true'
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'chore: automated dependency updates'
        body: |
          This PR contains automated dependency updates.
          
          ## Changes
          - Updated npm dependencies to latest compatible versions
          - Applied security fixes where available
          
          ## Testing
          Please review the changes and run tests before merging.
          
          Auto-generated by GitHub Actions.
        branch: chore/dependency-updates
        delete-branch: true
