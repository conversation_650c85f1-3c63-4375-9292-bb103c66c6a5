import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getUserGames } from '@/lib/models/game';
import { getUserCampaignCredits } from '@/lib/models/campaignCredit';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Get user's campaigns (games)
    const campaigns = await getUserGames(userId);

    // Get campaign credits for all campaigns
    const campaignCredits = await getUserCampaignCredits(userId);

    // Create a map of campaign credits by campaign ID
    const creditsMap = new Map();
    campaignCredits.forEach(credit => {
      creditsMap.set(credit.campaignId.toString(), credit);
    });

    // Combine campaign data with credit information
    const campaignsWithCredits = campaigns.map(campaign => {
      const campaignId = campaign._id.toString();
      const creditInfo = creditsMap.get(campaignId);

      // Calculate total prizes from campaign prizes
      const totalPrizes = campaign.prizes?.reduce((sum: number, prize: any) => sum + prize.quantity, 0) || 0;
      const remainingPrizes = campaign.prizes?.reduce((sum: number, prize: any) => sum + prize.remainingQuantity, 0) || 0;

      return {
        id: campaignId,
        name: campaign.name,
        type: campaign.type,
        instagramHandle: campaign.instagramHandle,
        pageCategory: campaign.pageCategory,
        followerCount: campaign.followerCount,
        status: campaign.status,
        plays: campaign.plays || 0,
        conversions: campaign.conversions || 0,
        gameLink: campaign.gameLink,
        colorScheme: campaign.colorScheme,
        createdAt: campaign.createdAt,
        updatedAt: campaign.updatedAt,
        lastActive: campaign.lastActive,
        // Prize/Credit information
        totalPrizes,
        remainingPrizes,
        usedPrizes: totalPrizes - remainingPrizes,
        // Campaign credit information (if exists)
        campaignCredits: creditInfo ? {
          totalCredits: creditInfo.totalCredits,
          remainingCredits: creditInfo.remainingCredits,
          usedCredits: creditInfo.usedCredits,
        } : {
          totalCredits: totalPrizes,
          remainingCredits: remainingPrizes,
          usedCredits: totalPrizes - remainingPrizes,
        },
        // Prizes array for detailed view
        prizes: campaign.prizes || [],
      };
    });

    return NextResponse.json({
      campaigns: campaignsWithCredits,
      total: campaignsWithCredits.length,
    });
  } catch (error) {
    console.error('API Error fetching campaigns:', error);
    return NextResponse.json({ error: 'Failed to fetch campaigns' }, { status: 500 });
  }
}
