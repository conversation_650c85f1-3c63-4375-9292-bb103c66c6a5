import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getCampaignCredit } from '@/lib/models/campaignCredit';
import { getGameById } from '@/lib/models/game';

export async function POST(
  request: Request,
  { params }: { params: { campaignId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { amount } = body;

    // Validate amount
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json({ error: 'Invalid credit amount' }, { status: 400 });
    }

    const { campaignId } = params;
    const userId = session.user.id;

    // Verify campaign exists and belongs to user
    const campaign = await getGameById(campaignId);
    if (!campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    if (campaign.userId.toString() !== userId) {
      return NextResponse.json({ error: 'Unauthorized access to campaign' }, { status: 403 });
    }

    // Get campaign credit record
    const campaignCredit = await getCampaignCredit(userId, campaignId);
    if (!campaignCredit) {
      return NextResponse.json({ error: 'Campaign credit record not found' }, { status: 404 });
    }

    // Create payment request for campaign-specific credits
    const paymentResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/payment/request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('Cookie') || '', // Forward session cookie
      },
      body: JSON.stringify({
        type: 'campaign_credits',
        amount,
        campaignId,
        campaignName: campaign.name,
      }),
    });

    if (!paymentResponse.ok) {
      const errorData = await paymentResponse.json();
      return NextResponse.json(
        {
          error: errorData.error || 'Failed to create payment request',
        },
        { status: paymentResponse.status }
      );
    }

    const paymentData = await paymentResponse.json();

    return NextResponse.json({
      success: true,
      paymentUrl: paymentData.paymentUrl,
      authority: paymentData.authority,
      amount,
      campaignId,
      campaignName: campaign.name,
    });
  } catch (error) {
    console.error('API Error creating campaign credit purchase:', error);
    return NextResponse.json({ error: 'Failed to create campaign credit purchase' }, { status: 500 });
  }
}
