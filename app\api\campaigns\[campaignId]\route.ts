import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getGameById } from '@/lib/models/game';
import { getCampaignCredit } from '@/lib/models/campaignCredit';

export async function GET(
  request: Request,
  { params }: { params: { campaignId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { campaignId } = params;
    const userId = session.user.id;

    // Get campaign (game) details
    const campaign = await getGameById(campaignId);

    if (!campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    // Verify campaign belongs to user
    if (campaign.userId.toString() !== userId) {
      return NextResponse.json({ error: 'Unauthorized access to campaign' }, { status: 403 });
    }

    // Get campaign credit information
    const campaignCredit = await getCampaignCredit(userId, campaignId);

    // Calculate prize information
    const totalPrizes = campaign.prizes?.reduce((sum: number, prize: any) => sum + prize.quantity, 0) || 0;
    const remainingPrizes = campaign.prizes?.reduce((sum: number, prize: any) => sum + prize.remainingQuantity, 0) || 0;

    // Prepare response data
    const campaignDetails = {
      id: campaign._id.toString(),
      name: campaign.name,
      type: campaign.type,
      instagramHandle: campaign.instagramHandle,
      pageCategory: campaign.pageCategory,
      followerCount: campaign.followerCount,
      status: campaign.status,
      plays: campaign.plays || 0,
      conversions: campaign.conversions || 0,
      gameLink: campaign.gameLink,
      colorScheme: campaign.colorScheme,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
      lastActive: campaign.lastActive,
      // Prize information
      totalPrizes,
      remainingPrizes,
      usedPrizes: totalPrizes - remainingPrizes,
      // Campaign credit information
      campaignCredits: campaignCredit ? {
        totalCredits: campaignCredit.totalCredits,
        remainingCredits: campaignCredit.remainingCredits,
        usedCredits: campaignCredit.usedCredits,
      } : {
        totalCredits: totalPrizes,
        remainingCredits: remainingPrizes,
        usedCredits: totalPrizes - remainingPrizes,
      },
      // Detailed prizes array
      prizes: campaign.prizes || [],
      // Game interactions for analytics
      gameInteractions: campaign.gameInteractions || {
        plays: [],
        conversions: [],
        codeVerifications: [],
        phoneVerifications: [],
        sessions: [],
      },
      sessionMetrics: campaign.sessionMetrics || {
        totalSessions: 0,
        averageSessionTime: 0,
        completionRate: 0,
        lastCalculated: new Date(),
      },
    };

    return NextResponse.json(campaignDetails);
  } catch (error) {
    console.error('API Error fetching campaign details:', error);
    return NextResponse.json({ error: 'Failed to fetch campaign details' }, { status: 500 });
  }
}
