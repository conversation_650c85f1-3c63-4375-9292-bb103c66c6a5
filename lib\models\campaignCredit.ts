import type { ObjectId } from 'mongodb';
import clientPromise from '../mongodb';
import { ObjectId as ObjectIdClass } from 'mongodb';

export interface CampaignCreditTransaction {
  id: string;
  date: Date;
  amount: number;
  type: 'purchase' | 'usage' | 'refund' | 'payment_success' | 'payment_failed';
  description?: string;
  // ZarinPal payment information
  paymentInfo?: {
    authority?: string;
    refId?: number;
    cardPan?: string;
    cardHash?: string;
    fee?: number;
    paymentAmount?: number; // Amount in Tomans
    status?: 'pending' | 'completed' | 'failed' | 'cancelled';
    gateway?: 'zarinpal';
  };
}

export interface CampaignCredit {
  _id?: ObjectId;
  userId: string | ObjectId;
  campaignId: string | ObjectId;
  campaignName: string; // For easier reference
  totalCredits: number; // Total prizes purchased for this campaign
  remainingCredits: number; // Remaining prizes available
  usedCredits: number; // Prizes that have been awarded
  transactions?: CampaignCreditTransaction[];
  createdAt: Date;
  updatedAt: Date;
}

// Create a new campaign credit record
export async function createCampaignCredit(
  userId: string | ObjectId,
  campaignId: string | ObjectId,
  campaignName: string,
  initialCredits: number = 0
): Promise<CampaignCredit> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  // Create initial transaction for free credits
  const initialTransaction: CampaignCreditTransaction = {
    id: new ObjectIdClass().toString(),
    date: new Date(),
    amount: initialCredits,
    type: 'purchase',
    description: initialCredits > 0 ? `جوایز رایگان کمپین جدید - ${initialCredits} جایزه` : 'ایجاد کمپین جدید',
  };

  const newCampaignCredit: CampaignCredit = {
    userId: userObjectId,
    campaignId: campaignObjectId,
    campaignName,
    totalCredits: initialCredits,
    remainingCredits: initialCredits,
    usedCredits: 0,
    transactions: initialCredits > 0 ? [initialTransaction] : [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await collection.insertOne(newCampaignCredit);
  return { ...newCampaignCredit, _id: result.insertedId };
}

// Get campaign credit by campaign ID and user ID
export async function getCampaignCredit(
  userId: string | ObjectId,
  campaignId: string | ObjectId
): Promise<CampaignCredit | null> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  return collection.findOne({
    userId: userObjectId,
    campaignId: campaignObjectId,
  }) as Promise<CampaignCredit | null>;
}

// Get all campaign credits for a user
export async function getUserCampaignCredits(userId: string | ObjectId): Promise<CampaignCredit[]> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  const credits = await collection.find({ userId: userObjectId }).toArray();
  return credits as CampaignCredit[];
}

// Add credits to a campaign
export async function addCampaignCredits(
  userId: string | ObjectId,
  campaignId: string | ObjectId,
  amount: number,
  description?: string,
  paymentInfo?: CampaignCreditTransaction['paymentInfo']
): Promise<number> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  // Get current campaign credit record
  const existingCredit = await collection.findOne({
    userId: userObjectId,
    campaignId: campaignObjectId,
  });

  if (!existingCredit) {
    throw new Error('Campaign credit record not found');
  }

  // Create transaction record
  const transaction: CampaignCreditTransaction = {
    id: new ObjectIdClass().toString(),
    date: new Date(),
    amount,
    type: paymentInfo ? 'payment_success' : 'purchase',
    description: description || 'افزایش جوایز کمپین',
    paymentInfo,
  };

  // Update campaign credits
  const result = await collection.updateOne(
    { userId: userObjectId, campaignId: campaignObjectId },
    {
      $inc: {
        totalCredits: amount,
        remainingCredits: amount,
      },
      $push: { transactions: transaction },
      $set: { updatedAt: new Date() },
    }
  );

  if (result.modifiedCount === 0) {
    throw new Error('Failed to update campaign credits');
  }

  return existingCredit.remainingCredits + amount;
}

// Use credits from a campaign (when a prize is awarded)
export async function useCampaignCredits(
  userId: string | ObjectId,
  campaignId: string | ObjectId,
  amount: number = 1,
  description?: string
): Promise<boolean> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  // Get current campaign credit record
  const existingCredit = await collection.findOne({
    userId: userObjectId,
    campaignId: campaignObjectId,
  });

  if (!existingCredit || existingCredit.remainingCredits < amount) {
    return false; // Not enough credits
  }

  // Create transaction record
  const transaction: CampaignCreditTransaction = {
    id: new ObjectIdClass().toString(),
    date: new Date(),
    amount: -amount, // Negative for usage
    type: 'usage',
    description: description || 'استفاده از جایزه کمپین',
  };

  // Update campaign credits
  const result = await collection.updateOne(
    { userId: userObjectId, campaignId: campaignObjectId },
    {
      $inc: {
        remainingCredits: -amount,
        usedCredits: amount,
      },
      $push: { transactions: transaction },
      $set: { updatedAt: new Date() },
    }
  );

  return result.modifiedCount > 0;
}

// Record a transaction for campaign credits
export async function recordCampaignCreditTransaction(
  userId: string | ObjectId,
  campaignId: string | ObjectId,
  transaction: Omit<CampaignCreditTransaction, 'id' | 'date'>
): Promise<CampaignCreditTransaction> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  const newTransaction: CampaignCreditTransaction = {
    id: new ObjectIdClass().toString(),
    date: new Date(),
    ...transaction,
  };

  await collection.updateOne(
    { userId: userObjectId, campaignId: campaignObjectId },
    {
      $push: { transactions: newTransaction },
      $set: { updatedAt: new Date() },
    }
  );

  return newTransaction;
}

// Get campaign credit transactions
export async function getCampaignCreditTransactions(
  userId: string | ObjectId,
  campaignId: string | ObjectId
): Promise<CampaignCreditTransaction[]> {
  const campaignCredit = await getCampaignCredit(userId, campaignId);
  return campaignCredit?.transactions || [];
}

// Delete campaign credit record (when campaign is deleted)
export async function deleteCampaignCredit(userId: string | ObjectId, campaignId: string | ObjectId): Promise<boolean> {
  const client = await clientPromise;
  const collection = client.db().collection('campaignCredits');

  // Ensure ObjectId format
  let userObjectId = userId;
  let campaignObjectId = campaignId;

  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    userObjectId = new ObjectIdClass(userId);
  }

  if (typeof campaignId === 'string' && ObjectIdClass.isValid(campaignId)) {
    campaignObjectId = new ObjectIdClass(campaignId);
  }

  const result = await collection.deleteOne({
    userId: userObjectId,
    campaignId: campaignObjectId,
  });

  return result.deletedCount > 0;
}
