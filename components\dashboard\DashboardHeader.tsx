'use client';

import { useState, useEffect } from 'react';
import { Bell, ChevronDown, Menu, Settings, User, LogOut, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { useSidebarMobile } from '@/hooks/use-sidebar-mobile';
import { useAuth } from '@/lib/auth-context';

interface Campaign {
  id: string;
  name: string;
  campaignCredits: {
    remainingCredits: number;
    totalCredits: number;
  };
}

export default function DashboardHeader() {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showCampaignDropdown, setShowCampaignDropdown] = useState(false);
  const { toggleSidebar } = useSidebarMobile();
  const { user, signOut } = useAuth();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const router = useRouter();

  // Fetch user campaigns
  useEffect(() => {
    const fetchCampaigns = async () => {
      if (user?.id) {
        try {
          const response = await fetch(`/api/campaigns`);
          if (response.ok) {
            const data = await response.json();
            setCampaigns(data.campaigns || []);
          }
        } catch (error) {
          console.error('Error fetching campaigns:', error);
        }
      }
    };

    fetchCampaigns();
  }, [user]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        setShowNotifications(false);
        setShowUserMenu(false);
        setShowCampaignDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header
      className="sticky top-0 z-20 border-b px-4 py-3 flex items-center justify-between bg-white"
      style={{ borderColor: 'var(--border)' }}
    >
      {/* Mobile Menu Button */}
      <Button variant="ghost" size="icon" className="md:hidden" onClick={toggleSidebar} aria-label="Toggle menu">
        <Menu className="h-5 w-5" />
      </Button>

      {/* Empty space for layout */}
      <div className="flex-1"></div>

      {/* Right Side Actions */}
      <div className="flex items-center gap-2">
        {/* Campaign Credits Dropdown */}
        <div className="hidden md:flex items-center gap-2">
          <div className="relative dropdown-container">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => setShowCampaignDropdown(!showCampaignDropdown)}
            >
              <Plus className="h-3 w-3" />
              افزودن جایزه
              <ChevronDown className="h-3 w-3" />
            </Button>

            <AnimatePresence>
              {showCampaignDropdown && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute left-0 mt-2 w-80 rounded-md shadow-lg overflow-hidden z-30"
                  style={{ backgroundColor: 'var(--background)', border: '1px solid var(--border)' }}
                >
                  <div
                    className="p-3 border-b font-medium"
                    style={{
                      borderColor: 'var(--border)',
                      color: 'var(--text-primary)',
                    }}
                  >
                    انتخاب کمپین برای افزودن جایزه
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {campaigns.length === 0 ? (
                      <div className="p-4 text-center text-sm" style={{ color: 'var(--text-secondary)' }}>
                        هیچ کمپینی یافت نشد
                      </div>
                    ) : (
                      campaigns.map((campaign) => (
                        <div
                          key={campaign.id}
                          className="p-3 border-b hover:bg-gray-50 cursor-pointer"
                          style={{ borderColor: 'var(--border)' }}
                          onClick={() => {
                            router.push(`/campaigns/${campaign.id}/credits/purchase`);
                            setShowCampaignDropdown(false);
                          }}
                        >
                          <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                            {campaign.name}
                          </p>
                          <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                            جوایز باقیمانده: {campaign.campaignCredits?.remainingCredits || 0}/
                            {campaign.campaignCredits?.totalCredits || 0}
                          </p>
                        </div>
                      ))
                    )}
                  </div>
                  <div className="p-2 text-center border-t" style={{ borderColor: 'var(--border)' }}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs w-full"
                      style={{ color: 'var(--primary)' }}
                      onClick={() => {
                        router.push('/dashboard/credits');
                        setShowCampaignDropdown(false);
                      }}
                    >
                      مدیریت اعتبارات قدیمی
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Notifications */}
        <div className="relative dropdown-container">
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <Bell className="h-5 w-5" />
            <span
              className="absolute top-1 left-1 w-2 h-2 rounded-full"
              style={{ backgroundColor: 'var(--secondary)' }}
            ></span>
          </Button>

          <AnimatePresence>
            {showNotifications && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute left-0 mt-2 w-80 rounded-md shadow-lg overflow-hidden z-30"
                style={{ backgroundColor: 'var(--background)' }}
              >
                <div
                  className="p-3 border-b font-medium"
                  style={{
                    borderColor: 'var(--border)',
                    color: 'var(--text-primary)',
                  }}
                >
                  اعلان‌ها
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="p-3 border-b hover:bg-gray-50 cursor-pointer"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                        بازی جدید ثبت شد
                      </p>
                      <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                        کسی بازی "فروش تابستانی" شما را بازی کرد
                      </p>
                      <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                        {i * 5} دقیقه پیش
                      </p>
                    </div>
                  ))}
                </div>
                <div className="p-2 text-center">
                  <Button variant="ghost" size="sm" className="text-xs w-full" style={{ color: 'var(--primary)' }}>
                    مشاهده همه اعلان‌ها
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Settings */}
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>

        {/* User Menu */}
        <div className="relative dropdown-container">
          <Button variant="ghost" className="flex items-center gap-2" onClick={() => setShowUserMenu(!showUserMenu)}>
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              <User className="h-4 w-4 text-white" />
            </div>
            <span className="hidden sm:inline text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
              {user?.name || 'صاحب فروشگاه'}
            </span>
            <ChevronDown className="h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
          </Button>

          <AnimatePresence>
            {showUserMenu && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute left-0 mt-2 w-48 rounded-md shadow-lg overflow-hidden z-30"
                style={{ backgroundColor: 'var(--background)' }}
              >
                <div className="py-1">
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      پروفایل شما
                    </button>
                  </Link>
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      تنظیمات حساب
                    </button>
                  </Link>
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      صورتحساب
                    </button>
                  </Link>
                  <div className="border-t my-1" style={{ borderColor: 'var(--border)' }}></div>
                  <button
                    className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50 flex items-center gap-2 text-red-500"
                    onClick={() => {
                      setShowUserMenu(false);
                      signOut();
                    }}
                  >
                    <LogOut className="h-4 w-4" />
                    خروج
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
}
