version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ENVIRONMENT_STATE: development
        NEXT_PUBLIC_BASE_URL: http://localhost:3000
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - ENVIRONMENT_STATE=development
      - NEXT_PUBLIC_BASE_URL=http://localhost:3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./deployment/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    restart: unless-stopped
    profiles:
      - with-nginx

networks:
  default:
    name: boostagram-network
