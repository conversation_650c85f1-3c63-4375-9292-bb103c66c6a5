'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { CreditCard, History, Plus, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  getCreditPackagesWithPrices,
  formatPrice,
  formatPricePerCredit,
  PRICE_PER_CREDIT_TOMANS,
  CURRENCY,
} from '@/lib/pricing-config';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import { useAuth } from '@/lib/auth-context';

interface Transaction {
  id: string | number;
  date: Date;
  amount: number;
  type: 'purchase' | 'usage' | 'package_purchase';
  description?: string;
  packageName?: string;
}

export default function CreditsPage() {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const [credits, setCredits] = useState<number | null>(null);
  const [purchaseAmount, setPurchaseAmount] = useState<number>(10);
  const [isLoading, setIsLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Fetch user credits and transactions
  useEffect(() => {
    const fetchCreditsAndTransactions = async () => {
      if (user?.id) {
        try {
          // Fetch credits
          const creditsResponse = await fetch(`/api/user/credits`);
          if (creditsResponse.ok) {
            const creditsData = await creditsResponse.json();
            setCredits(creditsData.credits);
          }

          // Fetch transactions
          const transactionsResponse = await fetch(`/api/user/credits/transactions`);
          if (transactionsResponse.ok) {
            const transactionsData = await transactionsResponse.json();
            setTransactions(
              transactionsData.transactions.map((t: any) => ({
                ...t,
                date: new Date(t.date),
              }))
            );
          }
        } catch (error) {
          console.error('Error fetching credits and transactions:', error);
          toast({
            title: 'خطا',
            description: 'بارگذاری اطلاعات اعتبار با شکست مواجه شد. لطفاً دوباره تلاش کنید.',
            variant: 'destructive',
          });
        }
      }
    };

    fetchCreditsAndTransactions();
  }, [user, toast]);

  // Check for payment result in URL parameters
  useEffect(() => {
    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');
    const refId = searchParams.get('ref_id');
    const error = searchParams.get('error');

    if (payment === 'success' && credits) {
      toast({
        title: 'پرداخت موفقیت‌آمیز!',
        description: `${credits} اعتبار به حساب شما اضافه شد.${refId ? ` کد پیگیری: ${refId}` : ''}`,
      });
      // Refresh credits and transactions
      if (user?.id) {
        fetch(`/api/user/credits`)
          .then((res) => res.json())
          .then((data) => setCredits(data.credits))
          .catch(console.error);

        fetch(`/api/user/credits/transactions`)
          .then((res) => res.json())
          .then((data) => setTransactions(data.transactions.map((t: any) => ({ ...t, date: new Date(t.date) }))))
          .catch(console.error);
      }
      // Clean URL
      router.replace('/dashboard/credits');
    } else if (payment === 'failed') {
      toast({
        title: 'پرداخت ناموفق',
        description:
          error === 'verification_failed' ? 'تأیید پرداخت با شکست مواجه شد.' : 'در پردازش پرداخت خطایی رخ داد.',
        variant: 'destructive',
      });
      // Clean URL
      router.replace('/dashboard/credits');
    } else if (payment === 'cancelled') {
      toast({
        title: 'پرداخت لغو شد',
        description: 'پرداخت توسط کاربر لغو شد.',
        variant: 'destructive',
      });
      // Clean URL
      router.replace('/dashboard/credits');
    }
  }, [searchParams, toast, router, user]);

  const handlePurchase = () => {
    if (purchaseAmount <= 0) {
      toast({
        title: 'مقدار نامعتبر',
        description: 'لطفاً تعداد مثبتی از اعتبارات برای خرید وارد کنید.',
        variant: 'destructive',
      });
      return;
    }

    setShowConfirmDialog(true);
  };

  const handlePaymentRedirect = async () => {
    setIsLoading(true);
    try {
      const totalAmount = purchaseAmount * PRICE_PER_CREDIT_TOMANS;

      const response = await fetch('/api/payment/zarinpal/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: totalAmount,
          description: `خرید ${purchaseAmount} اعتبار بازی`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment request');
      }

      const data = await response.json();

      // Check if we got a payment URL (new ZarinPal integration)
      if (data.payment_url) {
        toast({
          title: 'انتقال به درگاه پرداخت',
          description: 'در حال انتقال به درگاه پرداخت زرین‌پال...',
        });

        // Redirect to ZarinPal payment gateway
        window.location.href = data.payment_url;
        return;
      }

      // Legacy direct credit addition (fallback)
      if (data.credits) {
        setCredits(data.credits);

        // Dispatch a custom event to notify other components about the credit update
        window.dispatchEvent(
          new CustomEvent('credits-updated', {
            detail: { credits: data.credits },
          })
        );

        // Refresh transactions from server to get the real updated data
        const transactionsResponse = await fetch(`/api/user/credits/transactions`);
        if (transactionsResponse.ok) {
          const transactionsData = await transactionsResponse.json();
          setTransactions(
            transactionsData.transactions.map((t: any) => ({
              ...t,
              date: new Date(t.date),
            }))
          );
        }

        toast({
          title: 'خرید موفقیت‌آمیز!',
          description: `${purchaseAmount} اعتبار به حساب شما اضافه شد.`,
        });
        setPurchaseAmount(10); // Reset the form
      }
    } catch (error) {
      console.error('Payment redirect error:', error);
      toast({
        title: 'خرید ناموفق',
        description:
          error instanceof Error ? error.message : 'در پردازش خرید شما خطایی رخ داد. لطفاً دوباره تلاش کنید.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Header */}
          <DashboardHeader />

          {/* Content */}
          <main className="p-4 md:p-6 space-y-6">
            <div>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                مدیریت اعتبارات
              </h1>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                اعتبارات بازی خود را خریداری و مدیریت کنید
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Credit Balance Card */}
              <Card
                className="lg:col-span-1"
                style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
              >
                <CardHeader className="pb-3">
                  <CardTitle>موجودی فعلی</CardTitle>
                  <CardDescription>اعتبارات بازی موجود شما</CardDescription>
                </CardHeader>
                <CardContent className="text-center py-6">
                  <div className="flex justify-center mb-4">
                    <div className="w-20 h-20 rounded-full bg-purple-100 flex items-center justify-center">
                      <CreditCard className="h-10 w-10" style={{ color: 'var(--primary)' }} />
                    </div>
                  </div>
                  {credits !== null ? (
                    <div className="text-4xl font-bold" style={{ color: 'var(--primary)' }}>
                      {credits}
                    </div>
                  ) : (
                    <div className="h-10 flex items-center justify-center">
                      <RefreshCw className="h-6 w-6 animate-spin" style={{ color: 'var(--primary)' }} />
                    </div>
                  )}
                  <p className="text-sm mt-2" style={{ color: 'var(--text-secondary)' }}>
                    اعتبارات موجود
                  </p>
                </CardContent>
                <CardFooter className="flex justify-center pb-6">
                  <Button variant="outline" className="w-full" onClick={() => router.push('/dashboard/games')}>
                    مشاهده بازی‌های من
                  </Button>
                </CardFooter>
              </Card>

              {/* Purchase and History Tabs */}
              <Card
                className="lg:col-span-2"
                style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
              >
                <Tabs defaultValue="purchase" className="w-full">
                  <CardHeader className="pb-0">
                    <div className="flex items-center justify-between">
                      <CardTitle>مدیریت اعتبارات</CardTitle>
                      <TabsList>
                        <TabsTrigger value="purchase" className="text-sm">
                          خرید
                        </TabsTrigger>
                        <TabsTrigger value="history" className="text-sm">
                          تاریخچه
                        </TabsTrigger>
                      </TabsList>
                    </div>
                    <CardDescription>اعتبار خریداری کنید یا تاریخچه تراکنش‌هایتان را مشاهده کنید</CardDescription>
                  </CardHeader>

                  <CardContent className="pt-6">
                    <TabsContent value="purchase" className="mt-0 space-y-4">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="credit-amount">تعداد اعتبارات</Label>
                          <div className="flex items-center mt-1.5 space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => setPurchaseAmount(Math.max(1, purchaseAmount - 1))}
                              disabled={purchaseAmount <= 1}
                            >
                              -
                            </Button>
                            <Input
                              id="credit-amount"
                              type="number"
                              min="1"
                              value={purchaseAmount}
                              onChange={(e) => setPurchaseAmount(Number.parseInt(e.target.value) || 0)}
                              className="text-center"
                            />
                            <Button variant="outline" size="icon" onClick={() => setPurchaseAmount(purchaseAmount + 1)}>
                              +
                            </Button>
                          </div>
                        </div>

                        <div className="p-4 rounded-lg bg-gray-50">
                          <div className="flex justify-between mb-2">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              قیمت هر اعتبار:
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {formatPrice(PRICE_PER_CREDIT_TOMANS)}
                            </span>
                          </div>
                          <div className="flex justify-between mb-2">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              تعداد:
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {purchaseAmount}
                            </span>
                          </div>
                          <div className="flex justify-between pt-2 border-t">
                            <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                              مجموع:
                            </span>
                            <span className="font-bold" style={{ color: 'var(--primary)' }}>
                              {formatPrice(purchaseAmount * PRICE_PER_CREDIT_TOMANS)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="history" className="mt-0">
                      <div className="rounded-md border">
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b bg-gray-50">
                                <th className="px-4 py-3 text-right font-medium">تاریخ</th>
                                <th className="px-4 py-3 text-right font-medium">تراکنش</th>
                                <th className="px-4 py-3 text-left font-medium">مقدار</th>
                              </tr>
                            </thead>
                            <tbody>
                              {transactions.length === 0 ? (
                                <tr>
                                  <td colSpan={3} className="px-4 py-8 text-center text-gray-500">
                                    <div className="flex flex-col items-center">
                                      <History className="h-8 w-8 mb-2 text-gray-400" />
                                      <p>هنوز تراکنشی انجام نشده است</p>
                                      <p className="text-sm mt-1">
                                        پس از خرید اعتبار یا استفاده از بازی، تراکنش‌ها اینجا نمایش داده می‌شوند
                                      </p>
                                    </div>
                                  </td>
                                </tr>
                              ) : (
                                transactions.map((transaction) => (
                                  <tr key={transaction.id} className="border-b">
                                    <td className="px-4 py-3 text-right">{transaction.date.toLocaleDateString()}</td>
                                    <td className="px-4 py-3 text-right capitalize">
                                      {transaction.type === 'purchase' ? (
                                        <span className="flex items-center">
                                          <Plus className="ml-1 h-3 w-3 text-green-500" />
                                          خرید اعتبار
                                        </span>
                                      ) : transaction.type === 'package_purchase' ? (
                                        <span className="flex items-center">
                                          <Plus className="ml-1 h-3 w-3 text-green-500" />
                                          {transaction.description || 'خرید بسته'}
                                        </span>
                                      ) : (
                                        <span className="flex items-center">
                                          <History className="ml-1 h-3 w-3 text-blue-500" />
                                          استفاده از بازی
                                        </span>
                                      )}
                                    </td>
                                    <td
                                      className={`px-4 py-3 text-left font-medium ${
                                        transaction.type === 'purchase' || transaction.type === 'package_purchase'
                                          ? 'text-green-600'
                                          : 'text-blue-600'
                                      }`}
                                    >
                                      {transaction.type === 'purchase' || transaction.type === 'package_purchase'
                                        ? '+'
                                        : ''}
                                      {transaction.amount}
                                    </td>
                                  </tr>
                                ))
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </TabsContent>
                  </CardContent>

                  <CardFooter>
                    <TabsContent value="purchase" className="w-full mt-0">
                      <Button
                        className="w-full text-white"
                        style={{ backgroundColor: 'var(--primary)' }}
                        onClick={handlePurchase}
                        disabled={isLoading || purchaseAmount <= 0}
                      >
                        {isLoading ? (
                          <>
                            <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                            در حال پردازش...
                          </>
                        ) : (
                          <>خرید اعتبارات</>
                        )}
                      </Button>
                    </TabsContent>
                    <TabsContent value="history" className="w-full mt-0">
                      <Button variant="outline" className="w-full">
                        دانلود تاریخچه
                      </Button>
                    </TabsContent>
                  </CardFooter>
                </Tabs>
              </Card>
            </div>

            {/* Credit Packages */}
            <div>
              <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
                بسته‌های اعتباری
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {getCreditPackagesWithPrices().map((pkg) => (
                  <Card
                    key={pkg.name}
                    className="relative overflow-hidden"
                    style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
                  >
                    {pkg.popular && (
                      <div
                        className="absolute top-0 right-0 text-white px-3 py-1 text-xs font-medium"
                        style={{ backgroundColor: 'var(--primary)' }}
                      >
                        محبوب
                      </div>
                    )}
                    <CardHeader>
                      <CardTitle>{pkg.name}</CardTitle>
                      <CardDescription>{pkg.credits} اعتبار</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
                        {formatPrice(pkg.price)}
                      </div>
                      <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                        {formatPricePerCredit(pkg.pricePerCredit)}
                      </p>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center">
                          <svg
                            className="ml-2 h-4 w-4 text-green-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          {pkg.credits} بازی
                        </li>
                        <li className="flex items-center">
                          <svg
                            className="ml-2 h-4 w-4 text-green-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          بدون تاریخ انقضا
                        </li>
                        <li className="flex items-center">
                          <svg
                            className="ml-2 h-4 w-4 text-green-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          پشتیبانی {pkg.popular ? 'اولویت‌دار' : 'استاندارد'}
                        </li>
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className="w-full"
                        variant={pkg.popular ? 'default' : 'outline'}
                        onClick={() => {
                          setPurchaseAmount(pkg.credits);
                          setShowConfirmDialog(true);
                        }}
                      >
                        انتخاب بسته
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Purchase Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle className="text-center">تأیید خرید اعتبارات</DialogTitle>
            <DialogDescription className="text-center">لطفاً جزئیات خرید خود را بررسی کنید</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Purchase Summary */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">تعداد اعتبارات:</span>
                <span className="font-medium">{purchaseAmount}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">قیمت هر اعتبار:</span>
                <span className="font-medium">{formatPrice(PRICE_PER_CREDIT_TOMANS)}</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between items-center">
                  <span className="font-semibold">مجموع قابل پرداخت:</span>
                  <span className="font-bold text-lg text-purple-600">
                    {formatPrice(purchaseAmount * PRICE_PER_CREDIT_TOMANS)}
                  </span>
                </div>
              </div>
            </div>

            {/* Payment Info */}
            <div className="bg-blue-50 rounded-lg p-3 text-center text-sm text-blue-700">
              <p className="font-medium mb-1">💳 پرداخت امن از طریق زرین پال</p>
              <p>پس از کلیک روی دکمه پرداخت، به درگاه بانکی زرین پال منتقل خواهید شد</p>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)} className="w-full sm:w-auto">
              انصراف
            </Button>
            <Button
              onClick={handlePaymentRedirect}
              disabled={isLoading}
              className="w-full sm:w-auto bg-purple-600 hover:bg-purple-700"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                  در حال پردازش...
                </>
              ) : (
                'پرداخت و خرید'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
