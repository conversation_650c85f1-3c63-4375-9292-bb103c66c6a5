'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowRight, Gift, TrendingUp, Users, Eye, ShoppingCart, Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import PerformanceChart from '@/components/dashboard/PerformanceChart';

interface CampaignDetails {
  id: string;
  name: string;
  type: string;
  instagramHandle: string;
  pageCategory: string;
  followerCount: string;
  status: string;
  plays: number;
  conversions: number;
  gameLink: string;
  colorScheme: string;
  createdAt: string;
  updatedAt: string;
  lastActive: string;
  totalPrizes: number;
  remainingPrizes: number;
  usedPrizes: number;
  campaignCredits: {
    totalCredits: number;
    remainingCredits: number;
    usedCredits: number;
  };
  prizes: Array<{
    type: string;
    description: string;
    probability: number;
    quantity: number;
    remainingQuantity: number;
  }>;
}

export default function CampaignDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const campaignId = params.campaignId as string;

  useEffect(() => {
    const fetchCampaignDetails = async () => {
      try {
        const response = await fetch(`/api/campaigns/${campaignId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch campaign details');
        }

        const data = await response.json();
        setCampaign(data);
      } catch (error) {
        console.error('Error fetching campaign details:', error);
        setError('خطا در بارگذاری جزئیات کمپین');
        toast({
          title: 'خطا',
          description: 'خطا در بارگذاری جزئیات کمپین',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (campaignId) {
      fetchCampaignDetails();
    }
  }, [campaignId, toast]);

  const handleBuyCredits = () => {
    router.push(`/campaigns/${campaignId}/credits/purchase`);
  };

  const conversionRate = campaign ? Math.round((campaign.conversions / Math.max(campaign.plays, 1)) * 100) : 0;

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col" dir="rtl">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 overflow-y-auto">
            <DashboardHeader />
            <main className="p-4 md:p-6">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            </main>
          </div>
        </div>
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen flex flex-col" dir="rtl">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 overflow-y-auto">
            <DashboardHeader />
            <main className="p-4 md:p-6">
              <div className="text-center p-8">
                <h3 className="text-lg font-medium mb-2">خطا در بارگذاری کمپین</h3>
                <p className="text-sm text-gray-500 mb-4">{error}</p>
                <Button onClick={() => router.push('/dashboard')}>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  بازگشت به داشبورد
                </Button>
              </div>
            </main>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden">
        <DashboardSidebar />
        <div className="flex-1 overflow-y-auto">
          <DashboardHeader />
          <main className="p-4 md:p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/dashboard')}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  بازگشت
                </Button>
                <div>
                  <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                    {campaign.name}
                  </h1>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    جزئیات و آنالیتیکس کمپین
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={campaign.status === 'active' ? 'default' : 'secondary'}>
                  {campaign.status === 'active' ? 'فعال' : 'غیرفعال'}
                </Badge>
                <Button
                  onClick={handleBuyCredits}
                  className="flex items-center gap-2"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--button-text)',
                  }}
                >
                  <Plus className="h-4 w-4" />
                  خرید جایزه
                </Button>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">جوایز باقیمانده</CardTitle>
                  <Gift className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {campaign.campaignCredits.remainingCredits}/{campaign.campaignCredits.totalCredits}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {campaign.campaignCredits.usedCredits} جایزه استفاده شده
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">کل بازی‌ها</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{campaign.plays.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">تعداد کل شرکت‌کنندگان</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">تبدیل‌ها</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{campaign.conversions.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">برندگان جایزه</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">نرخ تبدیل</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{conversionRate}%</div>
                  <p className="text-xs text-muted-foreground">درصد موفقیت</p>
                </CardContent>
              </Card>
            </div>

            {/* Campaign Info */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>اطلاعات کمپین</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">نوع کمپین</p>
                      <p className="text-sm">
                        {campaign.type === 'wheel' ? 'چرخ شانس' : campaign.type === 'lever' ? 'اهرم شانس' : 'جعبه هدیه'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">اینستاگرام</p>
                      <p className="text-sm">@{campaign.instagramHandle}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">دسته‌بندی</p>
                      <p className="text-sm">{campaign.pageCategory}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">فالوورها</p>
                      <p className="text-sm">{campaign.followerCount}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">لینک کمپین</p>
                    <div className="flex items-center gap-2">
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1">
                        {campaign.gameLink}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(campaign.gameLink, '_blank')}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>جوایز کمپین</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {campaign.prizes.map((prize, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{prize.description}</p>
                          <p className="text-sm text-muted-foreground">احتمال: {prize.probability}%</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {prize.remainingQuantity}/{prize.quantity}
                          </p>
                          <p className="text-xs text-muted-foreground">باقیمانده</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>عملکرد کمپین</CardTitle>
                <CardDescription>نمودار بازی‌ها و تبدیل‌ها در طول زمان</CardDescription>
              </CardHeader>
              <CardContent>
                <PerformanceChart />
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </div>
  );
}
