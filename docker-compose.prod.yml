version: '3.8'

services:
  app:
    image: ghcr.io/${GITHUB_REPOSITORY}:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - ENVIRONMENT_STATE=production
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}
      # Add other environment variables as needed
      # - DATABASE_URL=${DATABASE_URL}
      # - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      # - NEXTAUTH_URL=${NEXTAUTH_URL}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/default.conf:/etc/nginx/conf.d/default.conf
      # Add SSL certificates if needed
      # - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

networks:
  default:
    name: boostagram-production
