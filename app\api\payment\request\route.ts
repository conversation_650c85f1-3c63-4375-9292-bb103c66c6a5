import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { zarinPalService } from '@/lib/zarinpal-service';
import { formatPrice, PRICE_PER_CREDIT_TOMANS } from '@/lib/pricing-config';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Types for request/response
interface PaymentRequestBody {
  type: 'credits' | 'plan';
  amount?: number; // For credits: number of credits to purchase
  planId?: string; // For plan: plan identifier
  gameData?: any; // For plan: game data to be saved after successful payment
}

interface PaymentRequestResponse {
  success: boolean;
  paymentUrl?: string;
  authority?: string;
  message?: string;
  error?: string;
}

export async function POST(request: Request): Promise<NextResponse<PaymentRequestResponse>> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
        },
        { status: 401 }
      );
    }

    // Parse request body
    const body: PaymentRequestBody = await request.json();
    const { type, amount, planId, gameData } = body;

    // Validate request type
    if (!type || (type !== 'credits' && type !== 'plan')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid payment type. Must be "credits" or "plan"',
        },
        { status: 400 }
      );
    }

    let paymentAmount: number;
    let description: string;
    let metadata: any = {
      mobile: session.user.phoneNumber || '',
      email: session.user.email || '',
    };

    // Calculate payment amount and description based on type
    if (type === 'credits') {
      if (!amount || amount <= 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid credit amount',
          },
          { status: 400 }
        );
      }

      paymentAmount = amount * PRICE_PER_CREDIT_TOMANS;
      description = `خرید ${amount} اعتبار برای حساب کاربری`;
      metadata.order_id = `credits_${session.user.id}_${Date.now()}`;
      metadata.credits = amount;
    } else if (type === 'plan') {
      if (!planId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Plan ID is required',
          },
          { status: 400 }
        );
      }

      // Handle free plan - no payment needed, just create the game
      if (planId === 'free') {
        // For free plan, we don't need payment, just create the game directly
        if (gameData) {
          const { createGame } = await import('@/lib/models/game');
          const { updateUserCredits } = await import('@/lib/models/user');

          // Calculate total prizes for credit deduction
          const totalPrizes = gameData.prizes.reduce((sum: number, prize: any) => sum + prize.quantity, 0);

          // Update user credits for free plan
          await updateUserCredits(session.user.id, 'free', totalPrizes);

          // Create the game
          const gameResult = await createGame({
            ...gameData,
            userId: session.user.id,
          });

          return NextResponse.json({
            success: true,
            message: 'بازی رایگان با موفقیت ایجاد شد',
            gameId: gameResult._id.toString(),
            gameLink: gameResult.gameLink,
          });
        } else {
          return NextResponse.json(
            {
              success: false,
              error: 'Game data is required for free plan',
            },
            { status: 400 }
          );
        }
      }

      // Define plan prices (you can move this to pricing-config.ts if needed)
      const planPrices = {
        starter: 50 * PRICE_PER_CREDIT_TOMANS,
        business: 200 * PRICE_PER_CREDIT_TOMANS,
        enterprise: 500 * PRICE_PER_CREDIT_TOMANS,
      };

      const planNames = {
        starter: 'شروع',
        business: 'کسب‌وکار',
        enterprise: 'سازمانی',
      };

      if (!planPrices[planId as keyof typeof planPrices]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid plan ID',
          },
          { status: 400 }
        );
      }

      paymentAmount = planPrices[planId as keyof typeof planPrices];
      description = `خرید پلن ${planNames[planId as keyof typeof planNames]} برای ایجاد بازی`;
      metadata.order_id = `plan_${planId}_${session.user.id}_${Date.now()}`;
      metadata.plan_id = planId;

      if (gameData) {
        metadata.has_game_data = true;
      }
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid payment type',
        },
        { status: 400 }
      );
    }

    // Store payment request in database for tracking
    const client = await clientPromise;
    const paymentsCollection = client.db().collection('payments');

    const paymentRecord = {
      userId: new ObjectId(session.user.id),
      type,
      amount: type === 'credits' ? amount : undefined,
      planId: type === 'plan' ? planId : undefined,
      gameData: type === 'plan' ? gameData : undefined,
      paymentAmount,
      description,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata,
    };

    const insertResult = await paymentsCollection.insertOne(paymentRecord);
    const paymentId = insertResult.insertedId.toString();

    // Add payment ID to metadata
    metadata.payment_id = paymentId;

    // Request payment from ZarinPal
    const paymentResult = await zarinPalService.requestPayment(paymentAmount, description, metadata);

    // Update payment record with authority
    await paymentsCollection.updateOne(
      { _id: insertResult.insertedId },
      {
        $set: {
          authority: paymentResult.authority,
          paymentUrl: paymentResult.paymentUrl,
          updatedAt: new Date(),
        },
      }
    );

    console.log('Payment request successful:', {
      paymentId,
      authority: paymentResult.authority,
      amount: paymentAmount,
      type,
    });

    return NextResponse.json({
      success: true,
      paymentUrl: paymentResult.paymentUrl,
      authority: paymentResult.authority,
      message: 'Payment request created successfully',
    });
  } catch (error) {
    console.error('Payment request error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create payment request',
      },
      { status: 500 }
    );
  }
}
