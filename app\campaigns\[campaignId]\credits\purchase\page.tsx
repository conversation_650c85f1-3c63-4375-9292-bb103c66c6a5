'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowRight, Gift, ShoppingCart, CreditCard } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import { PRICE_PER_CREDIT_TOMANS } from '@/lib/pricing-config';

interface CampaignInfo {
  id: string;
  name: string;
  campaignCredits: {
    totalCredits: number;
    remainingCredits: number;
    usedCredits: number;
  };
}

export default function CampaignCreditsPurchasePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [campaign, setCampaign] = useState<CampaignInfo | null>(null);
  const [purchaseAmount, setPurchaseAmount] = useState<number>(10);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const campaignId = params.campaignId as string;

  useEffect(() => {
    const fetchCampaignInfo = async () => {
      try {
        const response = await fetch(`/api/campaigns/${campaignId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch campaign info');
        }

        const data = await response.json();
        setCampaign(data);
      } catch (error) {
        console.error('Error fetching campaign info:', error);
        setError('خطا در بارگذاری اطلاعات کمپین');
        toast({
          title: 'خطا',
          description: 'خطا در بارگذاری اطلاعات کمپین',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (campaignId) {
      fetchCampaignInfo();
    }
  }, [campaignId, toast]);

  const handlePurchase = async () => {
    if (purchaseAmount <= 0) {
      toast({
        title: 'مقدار نامعتبر',
        description: 'لطفاً تعداد مثبتی از جوایز برای خرید وارد کنید.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const totalAmount = purchaseAmount * PRICE_PER_CREDIT_TOMANS;

      const response = await fetch(`/api/campaigns/${campaignId}/credits/purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: purchaseAmount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment request');
      }

      const data = await response.json();

      // Redirect to payment gateway
      if (data.paymentUrl) {
        window.location.href = data.paymentUrl;
      } else {
        throw new Error('Payment URL not received');
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      toast({
        title: 'خطا در پرداخت',
        description: error instanceof Error ? error.message : 'خطا در ایجاد درخواست پرداخت',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const totalCost = purchaseAmount * PRICE_PER_CREDIT_TOMANS;

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col" dir="rtl">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 overflow-y-auto">
            <DashboardHeader />
            <main className="p-4 md:p-6">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            </main>
          </div>
        </div>
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen flex flex-col" dir="rtl">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 overflow-y-auto">
            <DashboardHeader />
            <main className="p-4 md:p-6">
              <div className="text-center p-8">
                <h3 className="text-lg font-medium mb-2">خطا در بارگذاری کمپین</h3>
                <p className="text-sm text-gray-500 mb-4">{error}</p>
                <Button onClick={() => router.push('/dashboard')}>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  بازگشت به داشبورد
                </Button>
              </div>
            </main>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden">
        <DashboardSidebar />
        <div className="flex-1 overflow-y-auto">
          <DashboardHeader />
          <main className="p-4 md:p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/campaigns/${campaignId}`)}
                className="flex items-center gap-2"
              >
                <ArrowRight className="h-4 w-4" />
                بازگشت
              </Button>
              <div>
                <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                  خرید جایزه برای کمپین
                </h1>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                  {campaign.name}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Current Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="h-5 w-5" />
                    وضعیت فعلی جوایز
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-blue-600">
                        {campaign.campaignCredits.totalCredits}
                      </p>
                      <p className="text-sm text-muted-foreground">کل جوایز</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-green-600">
                        {campaign.campaignCredits.remainingCredits}
                      </p>
                      <p className="text-sm text-muted-foreground">باقیمانده</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-orange-600">
                        {campaign.campaignCredits.usedCredits}
                      </p>
                      <p className="text-sm text-muted-foreground">استفاده شده</p>
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-green-600 h-3 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          (campaign.campaignCredits.remainingCredits / 
                           Math.max(campaign.campaignCredits.totalCredits, 1)) * 100
                        }%`,
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Purchase Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    خرید جایزه جدید
                  </CardTitle>
                  <CardDescription>
                    هر جایزه {PRICE_PER_CREDIT_TOMANS.toLocaleString()} تومان
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">تعداد جوایز</Label>
                    <Input
                      id="amount"
                      type="number"
                      min="1"
                      value={purchaseAmount}
                      onChange={(e) => setPurchaseAmount(parseInt(e.target.value) || 0)}
                      placeholder="تعداد جوایز مورد نظر"
                    />
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span>تعداد جوایز:</span>
                      <span className="font-medium">{purchaseAmount}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span>قیمت هر جایزه:</span>
                      <span className="font-medium">{PRICE_PER_CREDIT_TOMANS.toLocaleString()} تومان</span>
                    </div>
                    <hr className="my-2" />
                    <div className="flex justify-between items-center font-bold text-lg">
                      <span>مجموع:</span>
                      <span>{totalCost.toLocaleString()} تومان</span>
                    </div>
                  </div>

                  <Button
                    onClick={handlePurchase}
                    disabled={isProcessing || purchaseAmount <= 0}
                    className="w-full flex items-center gap-2"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--button-text)',
                    }}
                  >
                    {isProcessing ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                    ) : (
                      <CreditCard className="h-4 w-4" />
                    )}
                    {isProcessing ? 'در حال پردازش...' : 'پرداخت و خرید'}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
