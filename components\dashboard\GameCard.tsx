'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Compass, Edit, Eye, Gift, MoreHorizontal, Trash2, Zap } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Game } from '@/types/game';

interface GameCardProps {
  game: Game;
  onViewDetails?: () => void;
}

export default function GameCard({ game, onViewDetails = () => {} }: GameCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Get the appropriate icon based on game type
  const GameIcon = game.type === 'wheel' ? Compass : game.type === 'lever' ? Zap : Gift;

  // Calculate conversion rate
  const conversionRate = Math.round((game.conversions / game.plays) * 100);

  return (
    <motion.div
      whileHover={{ y: -5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="rounded-lg overflow-hidden border shadow-sm relative"
      style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
    >
      {/* Card Header */}
      <div
        className={`p-4 relative ${
          game.type === 'wheel' ? 'bg-purple-100' : game.type === 'lever' ? 'bg-orange-100' : 'bg-green-100'
        }`}
      >
        <div className="flex justify-between items-start">
          <div
            className="w-10 h-10 rounded-full flex items-center justify-center"
            style={{
              backgroundColor:
                game.type === 'wheel'
                  ? 'rgba(139, 92, 246, 0.3)'
                  : game.type === 'lever'
                  ? 'rgba(249, 115, 22, 0.3)'
                  : 'rgba(132, 204, 22, 0.3)',
            }}
          >
            <GameIcon
              className="h-5 w-5"
              style={{
                color: game.type === 'wheel' ? '#8b5cf6' : game.type === 'lever' ? '#f97316' : '#84cc16',
              }}
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem className="flex items-center gap-2 cursor-pointer" onClick={onViewDetails}>
                <Eye className="h-4 w-4" />
                <span>مشاهده جزئیات</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                <Edit className="h-4 w-4" />
                <span>ویرایش</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex items-center gap-2 cursor-pointer text-red-500">
                <Trash2 className="h-4 w-4" />
                <span>حذف</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <h3 className="mt-3 font-medium truncate" style={{ color: 'var(--text-primary)' }}>
          {game.name}
        </h3>
        <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
          بازی {game.type === 'wheel' ? 'چرخ شانس' : game.type === 'lever' ? 'اهرم شانس' : 'جعبه هدیه'}
        </p>

        {/* Status Badge */}
        <div className="absolute bottom-4 left-4">
          <span
            className={`px-2 py-1 rounded-full text-xs ${
              game.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}
          >
            {game.status === 'active' ? 'فعال' : 'غیرفعال'}
          </span>
        </div>
      </div>

      {/* Card Body */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              کل بازی‌ها
            </p>
            <p className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
              {game.plays.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              تبدیل‌ها
            </p>
            <p className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
              {game.conversions.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              نرخ تبدیل
            </p>
            <p className="text-xs font-medium" style={{ color: 'var(--text-primary)' }}>
              {conversionRate}%
            </p>
          </div>
          <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full"
              style={{
                width: `${conversionRate}%`,
                backgroundColor: game.type === 'wheel' ? '#8b5cf6' : game.type === 'lever' ? '#f97316' : '#84cc16',
              }}
            ></div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border)' }}>
          <div className="flex justify-between items-center">
            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              آخرین فعالیت: {game.lastActive}
            </p>
            <Button
              size="sm"
              className="h-8 text-xs"
              onClick={onViewDetails}
              style={{
                backgroundColor: game.type === 'wheel' ? '#8b5cf6' : game.type === 'lever' ? '#f97316' : '#84cc16',
                color: 'white',
              }}
            >
              مشاهده جزئیات
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
