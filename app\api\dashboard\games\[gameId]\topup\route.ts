import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { topUpGamePrizes } from '@/lib/models/game';

export async function POST(request: Request, { params }: { params: { gameId: string } }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { gameId } = params;
    const body = await request.json();
    const { prizeIncrements } = body as { prizeIncrements: { description: string; quantity: number }[] };

    if (!Array.isArray(prizeIncrements) || prizeIncrements.length === 0) {
      return NextResponse.json({ error: 'Invalid prize increments' }, { status: 400 });
    }

    const result = await topUpGamePrizes(gameId, prizeIncrements);

    return NextResponse.json({ success: result.success, updatedCount: result.updatedCount });
  } catch (error) {
    console.error('Error topping up game prizes:', error);
    return NextResponse.json({ error: 'Failed to top up game prizes' }, { status: 500 });
  }
}

