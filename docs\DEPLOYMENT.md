# Deployment Guide

This document describes the CI/CD setup and deployment process for the Boostagram project.

## Overview

The project uses GitHub Actions for CI/CD with Docker containerization. The pipeline includes:

- **Continuous Integration**: Automated testing, linting, and building
- **Continuous Deployment**: Automated Docker image building and deployment
- **Multi-environment support**: Staging and Production environments

## Architecture

```
GitHub Repository
    ↓
GitHub Actions (CI/CD)
    ↓
GitHub Container Registry (ghcr.io)
    ↓
Docker Deployment (Staging/Production)
```

## CI/CD Pipeline

### Triggers
- **Push to `main`**: Triggers production deployment
- **Push to `develop`**: Triggers staging deployment
- **Pull Requests to `main`**: Runs tests and builds

### Pipeline Stages

1. **Test & Lint**
   - Install dependencies
   - Run ESLint
   - TypeScript type checking
   - Build application

2. **Build & Push Docker Image**
   - Build Docker image
   - Push to GitHub Container Registry
   - Tag with branch name and commit SHA

3. **Deploy**
   - Deploy to staging (develop branch)
   - Deploy to production (main branch)

## Setup Instructions

### 1. GitHub Repository Setup

1. Enable GitHub Actions in your repository
2. Enable GitHub Container Registry (ghcr.io)

### 2. Environment Variables

Set up the following secrets in your GitHub repository settings:

#### Repository Secrets
- `PRODUCTION_BASE_URL`: Your production domain (e.g., `https://boostagram.com`)
- `STAGING_BASE_URL`: Your staging domain (e.g., `https://staging.boostagram.com`)

#### Environment-specific Secrets
Create environments in GitHub (`staging` and `production`) and add:

**Production Environment:**
- `PRODUCTION_BASE_URL`
- Any other production-specific secrets

**Staging Environment:**
- `STAGING_BASE_URL`
- Any other staging-specific secrets

### 3. Server Setup

#### Prerequisites
- Docker and Docker Compose installed
- Git access to the repository
- Proper firewall configuration (ports 80, 443, 3000)

#### Server Configuration

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/boostagram.git
   cd boostagram
   ```

2. **Set up environment variables:**
   ```bash
   # For production
   export PRODUCTION_BASE_URL="https://your-domain.com"
   export GITHUB_REPOSITORY="your-username/boostagram"
   
   # For staging
   export STAGING_BASE_URL="https://staging.your-domain.com"
   ```

3. **Deploy using the script:**
   ```bash
   # Deploy to staging
   ./scripts/deploy.sh staging
   
   # Deploy to production
   ./scripts/deploy.sh production
   ```

## Manual Deployment

### Local Development
```bash
# Build and run locally
docker-compose up --build

# Run with nginx proxy
docker-compose --profile with-nginx up --build
```

### Production Deployment
```bash
# Pull latest image
docker pull ghcr.io/your-username/boostagram:latest

# Deploy with production compose file
docker-compose -f docker-compose.prod.yml up -d
```

## Monitoring and Maintenance

### Health Checks
- Health endpoint: `/api/health`
- Docker health checks configured
- Nginx proxy health monitoring

### Logs
```bash
# View application logs
docker-compose logs app

# View nginx logs
docker-compose logs nginx

# Follow logs in real-time
docker-compose logs -f
```

### Updates
The CI/CD pipeline automatically builds and deploys when code is pushed to the respective branches. For manual updates:

```bash
# Pull latest changes and redeploy
git pull origin main
./scripts/deploy.sh production
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check GitHub Actions logs
   - Verify all environment variables are set
   - Ensure Docker build context is correct

2. **Deployment Failures**
   - Check server resources (disk space, memory)
   - Verify Docker daemon is running
   - Check network connectivity to ghcr.io

3. **Application Not Starting**
   - Check application logs: `docker-compose logs app`
   - Verify environment variables
   - Check health endpoint: `curl http://localhost:3000/api/health`

### Debug Commands
```bash
# Check running containers
docker ps

# Check container logs
docker logs <container-id>

# Access container shell
docker exec -it <container-id> sh

# Check Docker images
docker images

# Clean up unused resources
docker system prune -a
```

## Security Considerations

- All secrets are stored in GitHub Secrets
- Docker images are scanned for vulnerabilities
- Nginx security headers are configured
- Non-root user in Docker containers
- Resource limits configured in production

## Performance Optimization

- Multi-stage Docker builds for smaller images
- Nginx gzip compression enabled
- Docker health checks for reliability
- Resource limits to prevent resource exhaustion
- Standalone Next.js output for optimal performance
