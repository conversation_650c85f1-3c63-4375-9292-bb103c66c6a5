#!/bin/bash

# Deployment script for Boostagram
# Usage: ./scripts/deploy.sh [environment]
# Environment: staging, production (default: staging)

set -e

ENVIRONMENT=${1:-staging}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🚀 Starting deployment for environment: $ENVIRONMENT"

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Invalid environment. Use 'staging' or 'production'"
    exit 1
fi

# Check if required environment variables are set
if [[ "$ENVIRONMENT" == "production" ]]; then
    if [[ -z "$PRODUCTION_BASE_URL" ]]; then
        echo "❌ PRODUCTION_BASE_URL environment variable is required for production deployment"
        exit 1
    fi
elif [[ "$ENVIRONMENT" == "staging" ]]; then
    if [[ -z "$STAGING_BASE_URL" ]]; then
        echo "❌ STAGING_BASE_URL environment variable is required for staging deployment"
        exit 1
    fi
fi

# Set environment-specific variables
if [[ "$ENVIRONMENT" == "production" ]]; then
    IMAGE_TAG="latest"
    BASE_URL="$PRODUCTION_BASE_URL"
    COMPOSE_FILE="docker-compose.prod.yml"
else
    IMAGE_TAG="develop"
    BASE_URL="$STAGING_BASE_URL"
    COMPOSE_FILE="docker-compose.yml"
fi

echo "📦 Pulling latest Docker image..."
docker pull "ghcr.io/$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^.]*\).*/\1/'):$IMAGE_TAG"

echo "🔧 Setting up environment variables..."
export GITHUB_REPOSITORY=$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^.]*\).*/\1/')
export NEXT_PUBLIC_BASE_URL="$BASE_URL"

echo "🐳 Starting services with Docker Compose..."
cd "$PROJECT_DIR"

if [[ "$ENVIRONMENT" == "production" ]]; then
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    docker-compose -f "$COMPOSE_FILE" up -d
else
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    docker-compose -f "$COMPOSE_FILE" up -d
fi

echo "⏳ Waiting for services to be healthy..."
sleep 30

# Health check
echo "🏥 Performing health check..."
if curl -f "http://localhost:3000/api/health" > /dev/null 2>&1; then
    echo "✅ Deployment successful! Application is healthy."
else
    echo "❌ Health check failed. Please check the logs."
    docker-compose -f "$COMPOSE_FILE" logs
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo "🌐 Application is available at: $BASE_URL"
