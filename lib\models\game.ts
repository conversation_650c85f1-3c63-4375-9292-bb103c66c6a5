import { ObjectId } from 'mongodb';
import clientPromise from '../mongodb';

// Restructure the Game interface to have specific interaction types
export interface Game {
  _id?: ObjectId;
  userId: string | ObjectId;
  name: string;
  type: 'wheel' | 'lever' | 'envelope';
  instagramHandle: string;
  pageCategory: string;
  followerCount: string;
  prizes: {
    type: string;
    description: string;
    probability: number;
    quantity: number;
    remainingQuantity: number;
  }[];
  colorScheme: string;
  gameLink: string;
  plays: number;
  conversions: number;
  status: 'active' | 'inactive' | 'draft';
  createdAt: Date;
  updatedAt: Date;
  lastActive: Date;
  // Organize interactions by specific types
  gameInteractions: {
    plays: {
      timestamp: Date;
      phoneNumber?: string;
    }[];
    conversions: {
      timestamp: Date;
      phoneNumber: string;
      prize: string;
    }[];
    codeVerifications: {
      timestamp: Date;
      phoneNumber: string;
      code?: string;
      success?: boolean;
    }[];
    phoneVerifications: {
      timestamp: Date;
      phoneNumber: string;
      success?: boolean;
    }[];
    sessions: {
      sessionId: string;
      startTime: Date;
      endTime?: Date;
      duration?: number; // in seconds
      phoneNumber?: string;
      completed: boolean;
      exitReason?: 'completed' | 'abandoned' | 'error';
    }[];
  };
  // Session analytics
  sessionMetrics: {
    totalSessions: number;
    averageSessionTime: number; // in seconds
    completionRate: number; // percentage
    lastCalculated: Date;
  };
}

export async function createGame(
  gameData: Omit<
    Game,
    '_id' | 'createdAt' | 'updatedAt' | 'lastActive' | 'plays' | 'conversions' | 'gameInteractions' | 'sessionMetrics'
  >
) {
  const client = await clientPromise;
  const collection = client.db().collection('games');

  // Generate a unique game ID
  const gameId = new ObjectId();

  // Ensure userId is stored consistently
  let userId = gameData.userId;

  // If userId is a string and valid ObjectId, convert it to ObjectId
  if (typeof userId === 'string' && ObjectId.isValid(userId)) {
    userId = new ObjectId(userId);
  }

  // Create the game with default values for tracking metrics
  const newGame = {
    ...gameData,
    userId: userId,
    plays: 0,
    conversions: 0,
    gameInteractions: {
      plays: [],
      conversions: [],
      codeVerifications: [],
      phoneVerifications: [],
      sessions: [],
    },
    sessionMetrics: {
      totalSessions: 0,
      averageSessionTime: 0,
      completionRate: 0,
      lastCalculated: new Date(),
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    lastActive: new Date(),
  };

  const result = await collection.insertOne(newGame);
  return { ...newGame, _id: result.insertedId };
}

// Session tracking functions
export async function startGameSession(gameId: string, sessionId: string) {
  try {
    console.log('DB: Starting session for game:', gameId, 'sessionId:', sessionId);
    const client = await clientPromise;
    const db = client.db();

    // Find the game first
    let actualGameId = gameId;
    let game = null;

    // Try to find by ObjectId first
    if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
      if (game) {
        actualGameId = gameId;
      }
    }

    // If not found, try to find by gameLink
    if (!game) {
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for session start');
        return false;
      }
    }

    // Create session record
    const sessionRecord = {
      sessionId,
      startTime: new Date(),
      completed: false,
    };

    // Add session to the game
    const result = await db.collection('games').updateOne(
      { _id: new ObjectId(actualGameId) },
      {
        $push: { 'gameInteractions.sessions': sessionRecord },
        $set: { lastActive: new Date(), updatedAt: new Date() },
      }
    );

    console.log('DB: Session started successfully');
    return result.modifiedCount > 0;
  } catch (error) {
    console.error('DB: Error starting session:', error);
    return false;
  }
}

export async function endGameSession(
  gameId: string,
  sessionId: string,
  exitReason: 'completed' | 'abandoned' | 'error' = 'completed',
  phoneNumber?: string,
  manualDuration?: number
) {
  try {
    console.log('DB: Ending session for game:', gameId, 'sessionId:', sessionId, 'reason:', exitReason);
    const client = await clientPromise;
    const db = client.db();

    // Find the game first
    let actualGameId = gameId;
    let game = null;

    // Try to find by ObjectId first
    if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
      if (game) {
        actualGameId = gameId;
      }
    }

    // If not found, try to find by gameLink
    if (!game) {
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for session end');
        return false;
      }
    }

    const endTime = new Date();

    // Prepare update object
    const updateFields: any = {
      'gameInteractions.sessions.$.endTime': endTime,
      'gameInteractions.sessions.$.completed': exitReason === 'completed',
      'gameInteractions.sessions.$.exitReason': exitReason,
      lastActive: new Date(),
      updatedAt: new Date(),
    };

    // Add phone number if provided
    if (phoneNumber) {
      updateFields['gameInteractions.sessions.$.phoneNumber'] = phoneNumber;
    }

    // Add manual duration if provided
    if (manualDuration !== undefined) {
      updateFields['gameInteractions.sessions.$.duration'] = manualDuration;
      console.log('DB: Setting manual duration:', manualDuration);
    }

    // Update the specific session
    const result = await db.collection('games').updateOne(
      {
        _id: new ObjectId(actualGameId),
        'gameInteractions.sessions.sessionId': sessionId,
      },
      {
        $set: updateFields,
      }
    );

    if (result.modifiedCount > 0) {
      // Calculate duration and update
      await calculateSessionDuration(actualGameId, sessionId);
      // Recalculate session metrics
      await recalculateSessionMetrics(actualGameId);
    }

    console.log('DB: Session ended successfully');
    return result.modifiedCount > 0;
  } catch (error) {
    console.error('DB: Error ending session:', error);
    return false;
  }
}

async function calculateSessionDuration(gameId: string, sessionId: string) {
  try {
    const client = await clientPromise;
    const db = client.db();

    // Get the game with the specific session
    const game = await db
      .collection('games')
      .findOne({ _id: new ObjectId(gameId) }, { projection: { 'gameInteractions.sessions': 1 } });

    if (!game || !game.gameInteractions?.sessions) return;

    const session = game.gameInteractions.sessions.find((s: any) => s.sessionId === sessionId);
    if (!session || !session.startTime || !session.endTime) return;

    const duration = Math.floor((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / 1000);

    // Update the session with calculated duration
    await db.collection('games').updateOne(
      {
        _id: new ObjectId(gameId),
        'gameInteractions.sessions.sessionId': sessionId,
      },
      {
        $set: {
          'gameInteractions.sessions.$.duration': duration,
        },
      }
    );

    console.log('DB: Session duration calculated:', duration, 'seconds');
  } catch (error) {
    console.error('DB: Error calculating session duration:', error);
  }
}

async function recalculateSessionMetrics(gameId: string) {
  try {
    const client = await clientPromise;
    const db = client.db();

    // Get all sessions for this game
    const game = await db
      .collection('games')
      .findOne({ _id: new ObjectId(gameId) }, { projection: { 'gameInteractions.sessions': 1 } });

    if (!game || !game.gameInteractions?.sessions) return;

    const sessions = game.gameInteractions.sessions;
    const completedSessions = sessions.filter((s: any) => s.completed && s.duration);

    const totalSessions = sessions.length;
    const averageSessionTime =
      completedSessions.length > 0
        ? Math.round(
            completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0) / completedSessions.length
          )
        : 0;
    const completionRate = totalSessions > 0 ? Math.round((completedSessions.length / totalSessions) * 100) : 0;

    // Update session metrics
    await db.collection('games').updateOne(
      { _id: new ObjectId(gameId) },
      {
        $set: {
          'sessionMetrics.totalSessions': totalSessions,
          'sessionMetrics.averageSessionTime': averageSessionTime,
          'sessionMetrics.completionRate': completionRate,
          'sessionMetrics.lastCalculated': new Date(),
          updatedAt: new Date(),
        },
      }
    );

    console.log('DB: Session metrics recalculated - avg:', averageSessionTime, 'completion:', completionRate);
  } catch (error) {
    console.error('DB: Error recalculating session metrics:', error);
  }
}

// Game model functions
export async function getGameById(gameId: string) {
  try {
    console.log('DB: Looking up game with ID:', gameId);
    const client = await clientPromise;
    const db = client.db();

    // First, try to find by direct match on the full gameId (for URL format)
    // This handles formats like "wheel-handle-id"
    let game = null;

    // Check if the gameId contains hyphens (indicating it's in the URL format)
    if (gameId.includes('-')) {
      console.log('DB: Detected URL format, extracting parts');
      const parts = gameId.split('-');

      // If we have at least 3 parts (type-handle-id), extract the ID
      if (parts.length >= 3) {
        const lastPart = parts[parts.length - 1];

        // If the last part is a valid ObjectId, use it
        if (ObjectId.isValid(lastPart)) {
          console.log('DB: Extracted valid ObjectId:', lastPart);
          game = await db.collection('games').findOne({ _id: new ObjectId(lastPart) });

          // If found, return it
          if (game) {
            console.log('DB: Found game by extracted ObjectId');
            return game;
          }
        }
      }

      // If not found by ID extraction, try to match the full gameLink
      console.log('DB: Trying to match full gameLink');
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game) {
        console.log('DB: Found game by gameLink match');
        return game;
      }
    }

    // If not found and it's a valid ObjectId, try direct lookup
    if (!game && ObjectId.isValid(gameId)) {
      console.log('DB: Looking up by direct ObjectId');
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });

      if (game) {
        console.log('DB: Found game by direct ObjectId');
        return game;
      }
    }

    // If still not found, try to match any part of the gameLink
    if (!game) {
      console.log('DB: Looking up by partial gameLink match');
      game = await db.collection('games').findOne({
        gameLink: {
          $regex: `/${gameId}$|/${gameId}/|${gameId}`,
          $options: 'i',
        },
      });

      if (game) {
        console.log('DB: Found game by partial gameLink match');
        return game;
      }
    }

    console.log('DB: Game not found with any method');
    return null;
  } catch (error) {
    console.error('DB: Error getting game by ID:', error);
    throw error;
  }
}

export async function getUserGames(userId: string) {
  try {
    console.log('DB: Getting games for user ID:', userId);
    const client = await clientPromise;
    const db = client.db();

    let games = [];

    // Try with ObjectId first
    if (ObjectId.isValid(userId)) {
      games = await db
        .collection('games')
        .find({ userId: new ObjectId(userId) })
        .sort({ createdAt: -1 })
        .toArray();

      console.log(`DB: Found ${games.length} games with ObjectId userId`);
    }

    // If no games found, try with string ID
    if (games.length === 0) {
      games = await db.collection('games').find({ userId: userId }).sort({ createdAt: -1 }).toArray();

      console.log(`DB: Found ${games.length} games with string userId`);
    }

    // If still no games, try with string representation of ObjectId
    if (games.length === 0 && ObjectId.isValid(userId)) {
      games = await db.collection('games').find({ userId: userId.toString() }).sort({ createdAt: -1 }).toArray();

      console.log(`DB: Found ${games.length} games with string representation of ObjectId userId`);
    }

    return games;
  } catch (error) {
    console.error('DB: Error getting user games:', error);
    throw error;
  }
}

export async function updateGameLink(gameId: string | ObjectId, newDomain: string) {
  const client = await clientPromise;
  const collection = client.db().collection('games');

  const game = (await collection.findOne({
    _id: new ObjectId(gameId.toString()),
  })) as Game | null;

  if (!game) {
    throw new Error('Game not found');
  }

  // Update the game link with the new domain
  const gameIdStr = game._id?.toString();
  const newGameLink = `${newDomain}/play/${game.type}-${game.instagramHandle.replace('@', '')}-${gameIdStr}`;

  await collection.updateOne(
    { _id: new ObjectId(gameId.toString()) },
    { $set: { gameLink: newGameLink, updatedAt: new Date() } }
  );

  return newGameLink;
}

// Update the incrementGamePlays function to record plays in the gameInteractions
export async function incrementGamePlays(gameId: string) {
  try {
    console.log('DB: Incrementing plays for game ID:', gameId);
    const client = await clientPromise;
    const db = client.db();

    // Try to find the game first using the same logic as getGameById
    let game = null;
    let actualGameId = gameId;

    // Check if the gameId contains hyphens (indicating it's in the URL format)
    if (gameId.includes('-')) {
      const parts = gameId.split('-');

      // If we have at least 3 parts (type-handle-id), extract the ID
      if (parts.length >= 3) {
        const lastPart = parts[parts.length - 1];

        // If the last part is a valid ObjectId, use it
        if (ObjectId.isValid(lastPart)) {
          actualGameId = lastPart;
          game = await db.collection('games').findOne({ _id: new ObjectId(lastPart) });
        }
      }
    } else if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
    }

    if (!game) {
      // Try to find by gameLink as a last resort
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for incrementing plays');
        return false;
      }
    }

    // Create a new play interaction
    const playInteraction = {
      timestamp: new Date(),
    };

    // Add the play to the gameInteractions.plays array and increment the plays counter
    const result = await db.collection('games').updateOne(
      { _id: new ObjectId(actualGameId) },
      {
        $inc: { plays: 1 },
        $set: { lastActive: new Date(), updatedAt: new Date() },
        $push: { 'gameInteractions.plays': playInteraction },
      }
    );

    console.log('DB: Play increment result:', result.modifiedCount > 0 ? 'Success' : 'Failed');
    return result.modifiedCount > 0;
  } catch (error) {
    console.error('DB: Error incrementing game plays:', error);
    throw error;
  }
}

// Update the recordGameConversion function to add to gameInteractions.conversions
export async function recordGameConversion(gameId: string, prize: string, phoneNumber: string) {
  try {
    console.log('DB: Recording conversion for game ID:', gameId);
    const client = await clientPromise;
    const db = client.db();

    // Try to find the game first using the same logic as getGameById
    let game = null;
    let actualGameId = gameId;

    // Check if the gameId contains hyphens (indicating it's in the URL format)
    if (gameId.includes('-')) {
      const parts = gameId.split('-');

      // If we have at least 3 parts (type-handle-id), extract the ID
      if (parts.length >= 3) {
        const lastPart = parts[parts.length - 1];

        // If the last part is a valid ObjectId, use it
        if (ObjectId.isValid(lastPart)) {
          actualGameId = lastPart;
          game = await db.collection('games').findOne({ _id: new ObjectId(lastPart) });
        }
      }
    } else if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
    }

    if (!game) {
      // Try to find by gameLink as a last resort
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for recording conversion');
        return false;
      }
    }

    // Create the conversion interaction
    const conversionInteraction = {
      phoneNumber,
      prize,
      timestamp: new Date(),
    };

    // Record the conversion in the separate collection
    await db.collection('conversions').insertOne({
      gameId: new ObjectId(actualGameId),
      prize,
      phoneNumber,
      timestamp: new Date(),
    });

    // Add the conversion to the gameInteractions.conversions array
    await db.collection('games').updateOne(
      { _id: new ObjectId(actualGameId) },
      {
        $inc: { conversions: 1 },
        $set: { updatedAt: new Date() },
        $push: { 'gameInteractions.conversions': conversionInteraction },
      }
    );

    console.log('DB: Successfully recorded conversion and added to gameInteractions');
    return true;
  } catch (error) {
    console.error('DB: Error recording game conversion:', error);
    throw error;
  }
}

// Update the recordGameInteraction function to handle specific interaction types
export async function recordGameInteraction(gameId: string, type: string, phoneNumber: string) {
  try {
    console.log('DB: Recording interaction for game ID:', gameId);
    const client = await clientPromise;
    const db = client.db();

    // Try to find the game first using the same logic as getGameById
    let game = null;
    let actualGameId = gameId;

    // Check if the gameId contains hyphens (indicating it's in the URL format)
    if (gameId.includes('-')) {
      const parts = gameId.split('-');

      // If we have at least 3 parts (type-handle-id), extract the ID
      if (parts.length >= 3) {
        const lastPart = parts[parts.length - 1];

        // If the last part is a valid ObjectId, use it
        if (ObjectId.isValid(lastPart)) {
          actualGameId = lastPart;
          game = await db.collection('games').findOne({ _id: new ObjectId(lastPart) });
        }
      }
    } else if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
    }

    if (!game) {
      // Try to find by gameLink as a last resort
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for recording interaction');
        return false;
      }
    }

    // Record the interaction in the separate collection for backward compatibility
    await db.collection('interactions').insertOne({
      gameId: new ObjectId(actualGameId),
      type,
      phoneNumber,
      timestamp: new Date(),
    });

    // Determine which interaction type to record
    if (type === 'code_verification') {
      // Create the code verification interaction
      const codeVerificationInteraction = {
        phoneNumber,
        timestamp: new Date(),
        success: true, // Default to true, can be updated if needed
      };

      // Add to the codeVerifications array
      await db.collection('games').updateOne(
        { _id: new ObjectId(actualGameId) },
        {
          $set: { updatedAt: new Date() },
          $push: {
            'gameInteractions.codeVerifications': codeVerificationInteraction,
          },
        }
      );
    } else if (type === 'phone_verification') {
      // Create the phone verification interaction
      const phoneVerificationInteraction = {
        phoneNumber,
        timestamp: new Date(),
        success: true, // Default to true, can be updated if needed
      };

      // Add to the phoneVerifications array
      await db.collection('games').updateOne(
        { _id: new ObjectId(actualGameId) },
        {
          $set: { updatedAt: new Date() },
          $push: {
            'gameInteractions.phoneVerifications': phoneVerificationInteraction,
          },
        }
      );
    } else {
      // For any other interaction types, we could add them to a generic interactions array if needed
      console.log(`DB: Unknown interaction type: ${type}`);
    }

    console.log('DB: Successfully recorded interaction and added to gameInteractions');
    return true;
  } catch (error) {
    console.error('DB: Error recording game interaction:', error);
    throw error;
  }
}

export async function decrementPrizeQuantity(gameId: string, prizeDescription: string) {
  try {
    console.log(`DB: Decrementing quantity for prize "${prizeDescription}" in game ID: ${gameId}`);
    const client = await clientPromise;
    const db = client.db();

    // Find the actual game ID using the same logic as other functions
    let actualGameId = gameId;
    let game = null;

    if (gameId.includes('-')) {
      const parts = gameId.split('-');
      if (parts.length >= 3) {
        const lastPart = parts[parts.length - 1];
        if (ObjectId.isValid(lastPart)) {
          actualGameId = lastPart;
          game = await db.collection('games').findOne({ _id: new ObjectId(lastPart) });
        }
      }
    } else if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
    }

    // If game not found using object ID, try using gameLink
    if (!game) {
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('DB: Game not found for decrementing prize quantity');
        return false;
      }
    }

    // First, check if the prize exists in the game and has remaining quantity
    const prizeExists = game.prizes.some(
      (p) => (p.description === prizeDescription || p.name === prizeDescription) && p.remainingQuantity > 0
    );

    if (!prizeExists) {
      console.log(`DB: Prize "${prizeDescription}" not found or has no remaining quantity`);
      return false;
    }

    // Decrement the prize quantity
    const result = await db.collection('games').updateOne(
      { _id: new ObjectId(actualGameId) },
      {
        $inc: { 'prizes.$[prize].remainingQuantity': -1 },
        $set: { updatedAt: new Date() },
      },
      {
        arrayFilters: [
          {
            $or: [{ 'prize.description': prizeDescription }, { 'prize.name': prizeDescription }],
          },
        ],
      }
    );

    if (result.modifiedCount === 0) {
      console.log('DB: No prize quantity was updated');
      return false;
    }

    // Get the updated game to check if the prize is now exhausted
    const updatedGame = await db.collection('games').findOne({ _id: new ObjectId(actualGameId) });
    if (!updatedGame) {
      console.log('DB: Could not retrieve updated game');
      return false;
    }

    // Find the prize that was just decremented
    const updatedPrize = updatedGame.prizes.find(
      (p) => p.description === prizeDescription || p.name === prizeDescription
    );

    // If the prize is now exhausted (remainingQuantity = 0), redistribute probabilities
    if (updatedPrize && updatedPrize.remainingQuantity === 0) {
      console.log(`DB: Prize "${prizeDescription}" is now exhausted. Redistributing probabilities.`);

      // Calculate the probability to redistribute
      const probabilityToRedistribute = updatedPrize.probability;

      // Set the exhausted prize's probability to 0
      const prizesWithUpdatedProbabilities = updatedGame.prizes.map((prize) => {
        if (prize.description === prizeDescription || prize.name === prizeDescription) {
          return { ...prize, probability: 0 };
        }
        return prize;
      });

      // Count how many prizes still have remaining quantity
      const availablePrizes = prizesWithUpdatedProbabilities.filter((prize) => prize.remainingQuantity > 0);

      // If there are still prizes available, redistribute the probability
      if (availablePrizes.length > 0) {
        // Calculate how much probability to add to each remaining prize
        const probabilityPerPrize = probabilityToRedistribute / availablePrizes.length;

        // Update the probabilities for all prizes
        const finalPrizes = prizesWithUpdatedProbabilities.map((prize) => {
          if (
            prize.remainingQuantity > 0 &&
            prize.description !== prizeDescription &&
            prize.name !== prizeDescription
          ) {
            return {
              ...prize,
              probability: Math.round((prize.probability + probabilityPerPrize) * 100) / 100,
            };
          }
          return prize;
        });

        // Update the game with the new prize probabilities
        await db.collection('games').updateOne(
          { _id: new ObjectId(actualGameId) },
          {
            $set: {
              prizes: finalPrizes,
              updatedAt: new Date(),
            },
          }
        );

        console.log('DB: Successfully redistributed probabilities');
      } else {
        console.log('DB: No prizes left with remaining quantity');
      }
    }

    return true;
  } catch (error) {
    console.error('DB: Error decrementing prize quantity:', error);
    throw error;
  }
}

// Function to get available prizes (those with remaining quantity > 0)
export async function getAvailablePrizes(gameId: string) {
  try {
    console.log('DB: Getting available prizes for game ID:', gameId);
    const client = await clientPromise;
    const db = client.db();

    // Find the game
    let game = null;

    if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
    } else {
      // Try to find by gameLink or other methods
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });
    }

    if (!game) {
      console.log('DB: Game not found for getting available prizes');
      return [];
    }

    // Filter prizes to only include those with remaining quantity > 0
    const availablePrizes = game.prizes.filter((prize) => prize.remainingQuantity > 0);

    console.log(`DB: Found ${availablePrizes.length} available prizes`);
    return availablePrizes;
  } catch (error) {
    console.error('DB: Error getting available prizes:', error);
    throw error;
  }
}
