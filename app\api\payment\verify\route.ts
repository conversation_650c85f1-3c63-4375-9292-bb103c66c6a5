import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { zarinPalService } from '@/lib/zarinpal-service';
import { addCreditsWithPayment, updateUserCredits } from '@/lib/models/user';
import { addCampaignCredits } from '@/lib/models/campaignCredit';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Types for request/response
interface PaymentVerifyBody {
  authority: string;
  status: string; // 'OK' or 'NOK' from ZarinPal callback
}

interface PaymentVerifyResponse {
  success: boolean;
  message: string;
  refId?: number;
  credits?: number;
  gameId?: string;
  gameLink?: string;
  error?: string;
}

export async function POST(request: Request): Promise<NextResponse<PaymentVerifyResponse>> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'لطفاً وارد حساب کاربری خود شوید',
        },
        { status: 401 }
      );
    }

    // Parse request body
    const body: PaymentVerifyBody = await request.json();
    const { authority, status } = body;

    if (!authority) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authority is required',
          message: 'کد تراکنش معتبر نیست',
        },
        { status: 400 }
      );
    }

    // Find payment record in database
    const client = await clientPromise;
    const paymentsCollection = client.db().collection('payments');

    const paymentRecord = await paymentsCollection.findOne({
      authority,
      userId: new ObjectId(session.user.id),
    });

    if (!paymentRecord) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment record not found',
          message: 'تراکنش مورد نظر یافت نشد',
        },
        { status: 404 }
      );
    }

    // Check if payment was already processed
    if (paymentRecord.status === 'completed') {
      return NextResponse.json({
        success: true,
        message: 'پرداخت قبلاً تأیید شده است',
        refId: paymentRecord.refId,
        credits: paymentRecord.type === 'credits' ? paymentRecord.amount : undefined,
        gameId: paymentRecord.gameId,
        gameLink: paymentRecord.gameLink,
      });
    }

    if (paymentRecord.status === 'failed') {
      return NextResponse.json(
        {
          success: false,
          message: 'پرداخت ناموفق بود',
          error: 'Payment already marked as failed',
        },
        { status: 400 }
      );
    }

    // Check if user cancelled the payment
    if (status === 'NOK') {
      await paymentsCollection.updateOne(
        { _id: paymentRecord._id },
        {
          $set: {
            status: 'cancelled',
            updatedAt: new Date(),
            cancelledAt: new Date(),
          },
        }
      );

      return NextResponse.json(
        {
          success: false,
          message: 'پرداخت توسط کاربر لغو شد',
          error: 'Payment cancelled by user',
        },
        { status: 400 }
      );
    }

    // Verify payment with ZarinPal
    const verificationResult = await zarinPalService.verifyPayment(authority, paymentRecord.paymentAmount);

    console.log('Payment verification result:', verificationResult);

    if (!verificationResult.success) {
      // Update payment record as failed
      await paymentsCollection.updateOne(
        { _id: paymentRecord._id },
        {
          $set: {
            status: 'failed',
            verificationResult,
            updatedAt: new Date(),
            failedAt: new Date(),
          },
        }
      );

      return NextResponse.json(
        {
          success: false,
          message: verificationResult.message,
          error: 'Payment verification failed',
        },
        { status: 400 }
      );
    }

    // Payment verified successfully - process based on type
    let responseData: any = {
      success: true,
      message: 'پرداخت با موفقیت انجام شد',
      refId: verificationResult.refId,
    };

    try {
      if (paymentRecord.type === 'credits') {
        // Add credits to user account with payment information
        const newCreditBalance = await addCreditsWithPayment(
          session.user.id,
          paymentRecord.amount,
          `خرید ${paymentRecord.amount} جایزه - شماره تراکنش: ${verificationResult.refId}`,
          {
            authority: paymentRecord.authority,
            refId: verificationResult.refId,
            cardPan: verificationResult.cardPan,
            cardHash: verificationResult.cardHash,
            fee: verificationResult.fee,
            paymentAmount: paymentRecord.paymentAmount,
            status: 'completed',
            gateway: 'zarinpal',
          }
        );

        responseData.credits = newCreditBalance;

        console.log('Credits added successfully:', {
          userId: session.user.id,
          amount: paymentRecord.amount,
          newBalance: newCreditBalance,
        });
      } else if (paymentRecord.type === 'campaign_credits') {
        // Add credits to specific campaign
        const newCampaignCreditBalance = await addCampaignCredits(
          session.user.id,
          paymentRecord.metadata.campaignId,
          paymentRecord.amount,
          `خرید ${paymentRecord.amount} جایزه برای کمپین - شماره تراکنش: ${verificationResult.refId}`,
          {
            authority: paymentRecord.authority,
            refId: verificationResult.refId,
            cardPan: verificationResult.cardPan,
            cardHash: verificationResult.cardHash,
            fee: verificationResult.fee,
            paymentAmount: paymentRecord.paymentAmount,
            status: 'completed',
            gateway: 'zarinpal',
          }
        );

        responseData.campaignCredits = newCampaignCreditBalance;
        responseData.campaignId = paymentRecord.metadata.campaignId;
        responseData.campaignName = paymentRecord.metadata.campaignName;

        console.log('Campaign credits added successfully:', {
          userId: session.user.id,
          campaignId: paymentRecord.metadata.campaignId,
          amount: paymentRecord.amount,
          newBalance: newCampaignCreditBalance,
        });
      } else if (paymentRecord.type === 'plan') {
        // Handle plan purchase - create game if gameData exists
        if (paymentRecord.gameData) {
          // Import the game creation logic
          const { createGame } = await import('@/lib/models/game');

          // Calculate total prizes for credit deduction
          const totalPrizes = paymentRecord.gameData.prizes.reduce(
            (sum: number, prize: any) => sum + prize.quantity,
            0
          );

          // Update user credits based on plan
          await updateUserCredits(session.user.id, paymentRecord.planId, totalPrizes);

          // Create the game
          const gameResult = await createGame({
            ...paymentRecord.gameData,
            userId: session.user.id,
          });

          responseData.gameId = gameResult.gameId;
          responseData.gameLink = gameResult.gameLink;

          console.log('Game created successfully after payment:', {
            gameId: gameResult.gameId,
            planId: paymentRecord.planId,
          });
        } else {
          // Just update credits for plan purchase without game creation
          const totalPrizes = 0; // No prizes to deduct
          await updateUserCredits(session.user.id, paymentRecord.planId, totalPrizes);
        }
      }

      // Update payment record as completed
      await paymentsCollection.updateOne(
        { _id: paymentRecord._id },
        {
          $set: {
            status: 'completed',
            verificationResult,
            refId: verificationResult.refId,
            cardPan: verificationResult.cardPan,
            cardHash: verificationResult.cardHash,
            fee: verificationResult.fee,
            completedAt: new Date(),
            updatedAt: new Date(),
            gameId: responseData.gameId,
            gameLink: responseData.gameLink,
          },
        }
      );

      return NextResponse.json(responseData);
    } catch (processingError) {
      console.error('Error processing successful payment:', processingError);

      // Update payment record with processing error
      await paymentsCollection.updateOne(
        { _id: paymentRecord._id },
        {
          $set: {
            status: 'processing_failed',
            verificationResult,
            processingError: processingError instanceof Error ? processingError.message : 'Unknown error',
            updatedAt: new Date(),
          },
        }
      );

      return NextResponse.json(
        {
          success: false,
          message: 'پرداخت تأیید شد اما در پردازش خطایی رخ داد. لطفاً با پشتیبانی تماس بگیرید.',
          error: 'Payment verified but processing failed',
          refId: verificationResult.refId,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to verify payment',
        message: 'خطا در تأیید پرداخت. لطفاً دوباره تلاش کنید.',
      },
      { status: 500 }
    );
  }
}
