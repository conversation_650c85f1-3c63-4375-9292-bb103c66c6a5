name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: reg.adrogame.com
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test and Lint

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Type check
        run: npx tsc --noEmit

      - name: Create .env file
        run: |
          echo "MONGODB_URI=${{ secrets.MONGODB_URI }}" > .env
          echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
          echo "NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }}" >> .env
          echo "NEXT_PUBLIC_SITE_URL=${{ vars.NEXT_PUBLIC_SITE_URL }}" >> .env
          echo "MELIPAYAMAK_USERNAME=${{ secrets.MELIPAYAMAK_USERNAME }}" >> .env
          echo "MELIPAYAMAK_PASSWORD=${{ secrets.MELIPAYAMAK_PASSWORD }}" >> .env
          echo "MELIPAYAMAK_FROM=${{ vars.MELIPAYAMAK_FROM }}" >> .env
          echo "SMS_USERNAME=${{ secrets.SMS_USERNAME }}" >> .env
          echo "SMS_PASSWORD=${{ secrets.SMS_PASSWORD }}" >> .env
          echo "GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}" >> .env
          echo "GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}" >> .env
          echo "ZARINPAL_MERCHANT_ID=${{ secrets.ZARINPAL_MERCHANT_ID }}" >> .env
          echo "ZARINPAL_CALLBACK_URL=${{ secrets.ZARINPAL_CALLBACK_URL }}" >> .env
          echo "ZARINPAL_SANDBOX=${{ github.ref == 'refs/heads/main' && 'false' || 'true' }}" >> .env
          echo "NEXTAUTH_DEBUG=false" >> .env
          echo "ENVIRONMENT_STATE=test" >> .env

      - name: Build application
        run: npm run build

      - name: Run tests
        run: npm test

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Harbor Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.HARBOR_USERNAME }}
          password: ${{ secrets.HARBOR_PASSWORD }}

      - name: Create .env file for Docker
        run: |
          echo "MONGODB_URI=${{ secrets.MONGODB_URI }}" > .env
          echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
          echo "NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }}" >> .env
          echo "NEXT_PUBLIC_SITE_URL=${{ vars.NEXT_PUBLIC_SITE_URL }}" >> .env
          echo "MELIPAYAMAK_USERNAME=${{ secrets.MELIPAYAMAK_USERNAME }}" >> .env
          echo "MELIPAYAMAK_PASSWORD=${{ secrets.MELIPAYAMAK_PASSWORD }}" >> .env
          echo "MELIPAYAMAK_FROM=${{ vars.MELIPAYAMAK_FROM }}" >> .env
          echo "SMS_USERNAME=${{ secrets.SMS_USERNAME }}" >> .env
          echo "SMS_PASSWORD=${{ secrets.SMS_PASSWORD }}" >> .env
          echo "GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}" >> .env
          echo "GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}" >> .env
          echo "ZARINPAL_MERCHANT_ID=${{ secrets.ZARINPAL_MERCHANT_ID }}" >> .env
          echo "ZARINPAL_CALLBACK_URL=${{ secrets.ZARINPAL_CALLBACK_URL }}" >> .env
          echo "ZARINPAL_SANDBOX=${{ github.ref == 'refs/heads/main' && 'false' || 'true' }}" >> .env
          echo "NEXTAUTH_DEBUG=false" >> .env

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image with Compose
        run: |
          docker-compose build \
            --build-arg ENVIRONMENT_STATE=${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }} \
            --build-arg NEXT_PUBLIC_BASE_URL=${{ github.ref == 'refs/heads/main' && secrets.PRODUCTION_BASE_URL || secrets.STAGING_BASE_URL }}
          docker-compose push

      - name: Test Docker image
        run: |
          docker-compose up -d
          sleep 10
          curl -f http://localhost:3000/api/health || exit 1
          docker-compose down

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create .env file for staging
        run: |
          echo "MONGODB_URI=${{ secrets.MONGODB_URI }}" > .env
          echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
          echo "NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }}" >> .env
          echo "NEXT_PUBLIC_SITE_URL=${{ vars.NEXT_PUBLIC_SITE_URL }}" >> .env
          echo "MELIPAYAMAK_USERNAME=${{ secrets.MELIPAYAMAK_USERNAME }}" >> .env
          echo "MELIPAYAMAK_PASSWORD=${{ secrets.MELIPAYAMAK_PASSWORD }}" >> .env
          echo "MELIPAYAMAK_FROM=${{ vars.MELIPAYAMAK_FROM }}" >> .env
          echo "SMS_USERNAME=${{ secrets.SMS_USERNAME }}" >> .env
          echo "SMS_PASSWORD=${{ secrets.SMS_PASSWORD }}" >> .env
          echo "GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}" >> .env
          echo "GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}" >> .env
          echo "ZARINPAL_MERCHANT_ID=${{ secrets.ZARINPAL_MERCHANT_ID }}" >> .env
          echo "ZARINPAL_CALLBACK_URL=${{ secrets.ZARINPAL_CALLBACK_URL }}" >> .env
          echo "ZARINPAL_SANDBOX=true" >> .env
          echo "NEXTAUTH_DEBUG=false" >> .env

      - name: Copy docker-compose.yml and .env to staging server
        env:
          DEPLOY_SSH_KEY: ${{ secrets.STAGING_SSH_KEY }}
          DEPLOY_SERVER: ${{ secrets.STAGING_SERVER }}
        run: |
          echo "$DEPLOY_SSH_KEY" > deploy_key
          chmod 600 deploy_key
          scp -i deploy_key .env docker-compose.yml user@$DEPLOY_SERVER:/path/to/project/
          rm deploy_key

      - name: Deploy to staging
        env:
          DEPLOY_SSH_KEY: ${{ secrets.STAGING_SSH_KEY }}
          DEPLOY_SERVER: ${{ secrets.STAGING_SERVER }}
        run: |
          ssh -i deploy_key user@$DEPLOY_SERVER << 'EOF'
            cd /path/to/project
            docker login -u ${{ secrets.HARBOR_USERNAME }} -p ${{ secrets.HARBOR_PASSWORD }} ${{ env.REGISTRY }}
            docker-compose pull
            docker-compose up -d --profile with-nginx
          EOF
          rm deploy_key

  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create .env file for production
        run: |
          echo "MONGODB_URI=${{ secrets.MONGODB_URI }}" > .env
          echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
          echo "NEXTAUTH_URL=${{ vars.NEXTAUTH_URL }}" >> .env
          echo "NEXT_PUBLIC_SITE_URL=${{ vars.NEXT_PUBLIC_SITE_URL }}" >> .env
          echo "MELIPAYAMAK_USERNAME=${{ secrets.MELIPAYAMAK_USERNAME }}" >> .env
          echo "MELIPAYAMAK_PASSWORD=${{ secrets.MELIPAYAMAK_PASSWORD }}" >> .env
          echo "MELIPAYAMAK_FROM=${{ vars.MELIPAYAMAK_FROM }}" >> .env
          echo "SMS_USERNAME=${{ secrets.SMS_USERNAME }}" >> .env
          echo "SMS_PASSWORD=${{ secrets.SMS_PASSWORD }}" >> .env
          echo "GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}" >> .env
          echo "GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}" >> .env
          echo "ZARINPAL_MERCHANT_ID=${{ secrets.ZARINPAL_MERCHANT_ID }}" >> .env
          echo "ZARINPAL_CALLBACK_URL=${{ secrets.ZARINPAL_CALLBACK_URL }}" >> .env
          echo "ZARINPAL_SANDBOX=false" >> .env
          echo "NEXTAUTH_DEBUG=false" >> .env

      - name: Copy docker-compose.yml and .env to production server
        env:
          DEPLOY_SSH_KEY: ${{ secrets.PRODUCTION_SSH_KEY }}
          DEPLOY_SERVER: ${{ secrets.PRODUCTION_SERVER }}
        run: |
          echo "$DEPLOY_SSH_KEY" > deploy_key
          chmod 600 deploy_key
          scp -i deploy_key .env docker-compose.yml user@$DEPLOY_SERVER:/path/to/project/
          rm deploy_key

      - name: Deploy to production
        env:
          DEPLOY_SSH_KEY: ${{ secrets.PRODUCTION_SSH_KEY }}
          DEPLOY_SERVER: ${{ secrets.PRODUCTION_SERVER }}
        run: |
          ssh -i deploy_key user@$DEPLOY_SERVER << 'EOF'
            cd /path/to/project
            docker login -u ${{ secrets.HARBOR_USERNAME }} -p ${{ secrets.HARBOR_PASSWORD }} ${{ env.REGISTRY }}
            docker-compose pull
            docker-compose up -d --profile with-nginx
          EOF
          rm deploy_key
