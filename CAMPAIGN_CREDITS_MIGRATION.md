# مایگریشن سیستم اعتبار به سیستم جوایز کمپین‌محور

## خلاصه تغییرات

این پروژه شامل تغییرات بزرگی در سیستم مدیریت اعتبار/جوایز است که از سیستم اعتبار کاربر-محور به سیستم جوایز کمپین‌محور تغییر کرده است.

### تغییرات اصلی:

1. **سیستم جوایز کمپین‌محور**: هر کمپین حالا جوایز جداگانه‌ای دارد
2. **مدل جدید پایگاه داده**: `CampaignCredit` برای مدیریت جوایز هر کمپین
3. **رابط کاربری جدید**: داشبورد با نمایش جوایز باقیمانده هر کمپین
4. **سیستم خرید جداگانه**: امکان خرید جایزه برای هر کمپین به صورت مجزا
5. **تغییر اصطلاحات**: از "اعتبار" به "جایزه"

## ساختار جدید

### مدل CampaignCredit
```typescript
interface CampaignCredit {
  _id?: ObjectId;
  userId: string | ObjectId;
  campaignId: string | ObjectId;
  campaignName: string;
  totalCredits: number;      // کل جوایز خریداری شده
  remainingCredits: number;  // جوایز باقیمانده
  usedCredits: number;       // جوایز استفاده شده
  transactions: CampaignCreditTransaction[];
  createdAt: Date;
  updatedAt: Date;
}
```

### API های جدید
- `GET /api/campaigns` - لیست کمپین‌ها با اطلاعات جوایز
- `GET /api/campaigns/[campaignId]` - جزئیات کمپین
- `GET /api/campaigns/[campaignId]/credits` - اطلاعات جوایز کمپین
- `POST /api/campaigns/[campaignId]/credits/purchase` - خرید جایزه برای کمپین

### صفحات جدید
- `/campaigns/[campaignId]` - صفحه جزئیات کمپین
- `/campaigns/[campaignId]/credits/purchase` - صفحه خرید جایزه

## مایگریشن داده‌ها

### اجرای مایگریشن
```bash
# مایگریشن داده‌های موجود
npm run migrate migrate

# تأیید صحت مایگریشن
npm run migrate verify

# اجرای هر دو
npm run migrate both
```

### فرآیند مایگریشن
1. **شناسایی کاربران**: پیدا کردن کاربرانی که اعتبار دارند
2. **شناسایی کمپین‌ها**: پیدا کردن کمپین‌های هر کاربر
3. **توزیع اعتبار**: تقسیم اعتبار کاربر بین کمپین‌هایش
4. **ایجاد رکوردها**: ساخت رکورد `CampaignCredit` برای هر کمپین

### نکات مهم مایگریشن
- اعتبارات اصلی کاربران حفظ می‌شود
- توزیع اعتبار بر اساس نیاز جوایز هر کمپین انجام می‌شود
- رکوردهای تراکنش مایگریشن ثبت می‌شود

## تست سیستم

### مراحل تست
1. **تست ایجاد کمپین جدید**
   - کمپین جدید باید رکورد `CampaignCredit` ایجاد کند
   - جوایز باید بر اساس تعداد prizes محاسبه شود

2. **تست خرید جایزه**
   - خرید جایزه برای کمپین خاص
   - بررسی به‌روزرسانی `remainingCredits`

3. **تست استفاده از جایزه**
   - اعطای جایزه در بازی
   - کاهش `remainingCredits` و افزایش `usedCredits`

4. **تست رابط کاربری**
   - نمایش صحیح جوایز باقیمانده در کارت‌های کمپین
   - عملکرد صفحه جزئیات کمپین
   - عملکرد صفحه خرید جایزه

### کوئری‌های تست
```javascript
// بررسی تعداد رکوردهای CampaignCredit
db.campaignCredits.countDocuments()

// بررسی کمپین‌های بدون رکورد اعتبار
db.games.aggregate([
  {
    $lookup: {
      from: "campaignCredits",
      localField: "_id",
      foreignField: "campaignId",
      as: "credits"
    }
  },
  {
    $match: {
      "credits": { $size: 0 }
    }
  }
])

// بررسی مجموع جوایز
db.campaignCredits.aggregate([
  {
    $group: {
      _id: null,
      totalCredits: { $sum: "$totalCredits" },
      remainingCredits: { $sum: "$remainingCredits" },
      usedCredits: { $sum: "$usedCredits" }
    }
  }
])
```

## مشکلات احتمالی و راه‌حل

### 1. کمپین‌های بدون رکورد اعتبار
**علت**: کمپین‌های ایجاد شده قبل از مایگریشن
**راه‌حل**: اجرای مجدد مایگریشن یا ایجاد دستی رکورد

### 2. عدم تطابق جوایز
**علت**: تغییر در prizes بعد از مایگریشن
**راه‌حل**: به‌روزرسانی دستی `CampaignCredit`

### 3. مشکلات TypeScript
**علت**: تغییرات در interface ها
**راه‌حل**: به‌روزرسانی type definitions

## بازگشت به سیستم قبلی (Rollback)

در صورت نیاز به بازگشت:

1. **حذف collection جدید**:
```javascript
db.campaignCredits.drop()
```

2. **بازگردانی کدهای قبلی** از git

3. **بررسی یکپارچگی داده‌ها**

## نکات عملکردی

### بهینه‌سازی‌ها
- Index روی `userId` و `campaignId` در `campaignCredits`
- Cache کردن اطلاعات جوایز در frontend
- Pagination برای لیست کمپین‌ها

### امنیت
- بررسی مالکیت کمپین قبل از خرید جایزه
- Validation مقادیر ورودی
- Rate limiting برای API های پرداخت

## پشتیبانی

برای مشکلات یا سوالات:
1. بررسی logs سرور
2. اجرای کوئری‌های تست
3. بررسی console browser برای خطاهای frontend

---

**تاریخ آخرین به‌روزرسانی**: 2025-01-08
**نسخه**: 2.0.0 - Campaign-Specific Credits System
